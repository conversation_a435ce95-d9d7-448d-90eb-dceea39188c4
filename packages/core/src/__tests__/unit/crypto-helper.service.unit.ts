import {expect} from '@loopback/testlab';
import {CryptoHelperService} from '../../services/crypto-helper.service';

describe('CryptoHelperService (unit)', () => {
  let service: CryptoHelperService;

  beforeEach(() => {
    service = new CryptoHelperService();
  });

  describe('generateComplexRandomString', () => {
    it('should generate a string of the specified length', () => {
      const length = 12;
      const result = service.generateComplexRandomString(length);
      expect(result).to.have.length(length);
    });

    it('should contain at least one uppercase letter', () => {
      const result = service.generateComplexRandomString(12);
      expect(/[A-Z]/.test(result)).to.be.true();
    });

    it('should contain at least one lowercase letter', () => {
      const result = service.generateComplexRandomString(12);
      expect(/[a-z]/.test(result)).to.be.true();
    });

    it('should contain at least one number', () => {
      const result = service.generateComplexRandomString(12);
      expect(/[0-9]/.test(result)).to.be.true();
    });

    it('should contain at least one special character', () => {
      const result = service.generateComplexRandomString(12);
      expect(/[@#$%^&*!+=?-]/.test(result)).to.be.true();
    });

    it('should throw error for length less than 8', () => {
      expect(() => service.generateComplexRandomString(7)).to.throw(
        'Password length must be at least 8 characters',
      );
    });

    it('should generate different strings on multiple calls', () => {
      const result1 = service.generateComplexRandomString(12);
      const result2 = service.generateComplexRandomString(12);
      expect(result1).to.not.equal(result2);
    });

    it('should use default length of 12 when no parameter provided', () => {
      const result = service.generateComplexRandomString();
      expect(result).to.have.length(12);
    });

    it('should only contain allowed characters', () => {
      const result = service.generateComplexRandomString(12);
      const allowedChars = /^[A-Za-z0-9@#$%^&*!+=?-]+$/;
      expect(allowedChars.test(result)).to.be.true();
    });
  });

  describe('generateRandomString', () => {
    it('should generate a hex string of the specified length', () => {
      const length = 10;
      const result = service.generateRandomString(length);
      expect(result).to.have.length(length);
      expect(/^[0-9a-f]+$/.test(result)).to.be.true();
    });
  });
});
