/**
 * Enum representing the types of notifications supported.
 */
export enum NotificationType {
  /**
   * SMS notification type.
   */
  SMS,

  /**
   * Email notification type.
   */
  EMAIL,

  /**
   * Push notification (e.g., mobile or browser) type.
   */
  PUSH,
}

/**
 * Enum representing the types of notification events.
 */
export enum NotificationEventType {
  /**
   * Event triggered for forget password flow.
   */
  ForgetPassword = 'forget_password',

  /**
   * Event triggered for sending OTP (One-Time Password).
   */
  OtpSend = 'otp_send',
  InvoicePaymentRequest = 'invoice_payment_link',
  /**
   * Event triggered for user added
   */
  UserAdded = 'user_added',
  /**
   * Event triggered when a subscription plan is updated.
   *
   * Used for notifying dependent services or sending
   * plan update related communications.
   */
  PlanUpdate = 'plan_update',
  TenantOnboarding = 'tenant_onboarding',
  /** Event triggered when a trial period is about to end */
  TrialEndingSoon = 'trial_ending_soon',
  ProvisionFailed = 'provision_failed',
  TenantReactivationSuccess = 'tenant_reactivation_success',
  TenantDeProvision = 'tenant_deprovisioning_success',
  TenantTrialSuspension = 'tenant_trial_suspension_success',
  TenantEditPlan = 'tenant_edit_plan',
  TenantPaymentSuspension = 'tenant_payment_suspension_success',
  UserReactivated = 'user_reactivated',
}

/**
 * A set of notification event types that require CC (carbon copy) email recipients.
 *
 * This set includes events such as invoice payment requests, trial ending notifications,
 * provisioning failures, user and tenant reactivations, tenant deprovisioning, trial suspensions,
 * and payment suspensions. When any of these events are triggered, CC email addresses should be included
 * in the notification.
 */
const requiredCCEmailEvents = new Set<string>([
  NotificationEventType.InvoicePaymentRequest,
  NotificationEventType.TrialEndingSoon,
  NotificationEventType.ProvisionFailed,
  NotificationEventType.UserReactivated,
  NotificationEventType.TenantReactivationSuccess,
  NotificationEventType.TenantDeProvision,
  NotificationEventType.TenantTrialSuspension,
  NotificationEventType.TenantPaymentSuspension,
]);

/**
 * Determines whether a notification event type requires sending to CC (carbon copy) recipients.
 *
 * @param eventType - The type of notification event to check. Can be a `NotificationEventType` or a `string`.
 * @returns `true` if the event type is included in the set of events that require CC emails; otherwise, `false`.
 */
export const canSendToCC = (eventType: NotificationEventType | string) =>
  requiredCCEmailEvents.has(eventType);
