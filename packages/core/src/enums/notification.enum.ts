/**
 * Enum representing the types of notifications supported.
 */
export enum NotificationType {
  /**
   * SMS notification.
   */
  SMS,

  /**
   * Email notification.
   */
  EMAIL,

  /**
   * Push notification (e.g., mobile or browser).
   */
  PUSH,
}

/**
 * Enum representing the types of notification events.
 */
export enum NotificationEventType {
  /**
   * Event triggered for forget password flow.
   */
  ForgetPassword = 'forget_password',

  /**
   * Event triggered for sending OTP (One-Time Password).
   */
  OtpSend = 'otp_send',
  InvoicePaymentRequest = 'invoice_payment_link',
  /**
   * Event triggered for user added
   */
  UserAdded = 'user_added',
  /**
   * Event triggered when a subscription plan is updated.
   *
   * Used for notifying dependent services or sending
   * plan update related communications.
   */
  PlanUpdate = 'plan_update',
  TenantOnboarding = 'tenant_onboarding',
  /** Event triggered when a trial period is about to end */
  TrialEndingSoon = 'trial_ending_soon',
}
