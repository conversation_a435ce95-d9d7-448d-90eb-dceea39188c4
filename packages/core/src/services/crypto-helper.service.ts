import {sign} from 'jsonwebtoken';
import {BindingScope, injectable} from '@loopback/core';
import {
  randomBytes,
  createCipheriv,
  createDecipheriv,
  createHmac,
} from 'crypto';
const FIVE_SECONDS = 5000;
@injectable({scope: BindingScope.SINGLETON})
export class CryptoHelperService {
  /**
   * The function generates a temporary token using a payload and an optional expiry time.
   * @param {T} payload - The `payload` parameter is an object that contains the data you want to include
   * in the token. This data can be any valid JSON object.
   * @param {number} [expiry] - The `expiry` parameter is an optional parameter that specifies the
   * expiration time for the generated token. It is a number that represents the duration in seconds
   * after which the token will expire. If no value is provided for `expiry`, the token will expire after
   * 5 seconds.
   * @returns a signed JWT token.
   */
  generateTempToken<T extends object>(payload: T, expiry?: number) {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error(
        'JWT_SECRET is not defined in the environment variables.',
      );
    }
    //sonarignore:start
    return sign(payload, secret, {
      //sonarignore:end
      issuer: process.env.JWT_ISSUER,
      algorithm: 'HS256',
      expiresIn: expiry ?? FIVE_SECONDS,
    });
  }

  /**
   * The function generates a random string of a specified length using random bytes.
   * @param {number} length - The length parameter is a number that specifies the desired length of the
   * random string to be generated.
   * @returns a randomly generated string of hexadecimal characters.
   */
  generateRandomString(length: number) {
    // divided by two as the result is twice the length given
    return randomBytes(length / 2).toString('hex');
  }

  /**
   * Generates a cryptographically secure random string with mixed case letters, numbers, and special characters.
   * The string will contain at least one uppercase letter, one lowercase letter, one number, and one special character.
   * @param {number} length - The desired length of the random string (minimum 8).
   * @returns {string} A random string containing uppercase letters, lowercase letters, numbers, and special characters.
   * @example
   * // Returns something like "Abhrajit@abc1"
   * generateComplexRandomString(12)
   */
  generateComplexRandomString(length = 12): string {
    if (length < 8) {
      throw new Error('Password length must be at least 8 characters');
    }

    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = '@#$%^&*!+=?-';

    /**
     * Generates a cryptographically secure random index for the given string length
     * @param max - Maximum value (exclusive)
     * @returns Random index between 0 and max-1
     */
    const getSecureRandomIndex = (max: number): number => {
      const randomBuffer = randomBytes(4);
      const randomValue = randomBuffer.readUInt32BE(0);
      return randomValue % max;
    };

    // Ensure at least one character from each category using secure random
    const requiredChars = [
      uppercase[getSecureRandomIndex(uppercase.length)],
      lowercase[getSecureRandomIndex(lowercase.length)],
      numbers[getSecureRandomIndex(numbers.length)],
      specialChars[getSecureRandomIndex(specialChars.length)],
    ];

    // Fill remaining length with random characters from all categories
    const allChars = uppercase + lowercase + numbers + specialChars;
    const remainingLength = length - requiredChars.length;

    for (let i = 0; i < remainingLength; i++) {
      requiredChars.push(allChars[getSecureRandomIndex(allChars.length)]);
    }

    // Shuffle the array using Fisher-Yates algorithm with secure random
    for (let i = requiredChars.length - 1; i > 0; i--) {
      const j = getSecureRandomIndex(i + 1);
      [requiredChars[i], requiredChars[j]] = [
        requiredChars[j],
        requiredChars[i],
      ];
    }

    return requiredChars.join('');
  }

  /**
   * Encrypts a password using AES-256-CBC encryption.
   * @param {string} password - The password to encrypt.
   * @returns {string} The encrypted password in the format "iv:encryptedData".
   * @throws {Error} If SECRET_KEY is not defined in environment variables.
   */
  encryptPassword(password: string): string {
    const secretKey = process.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error(
        'SECRET_KEY is not defined in the environment variables.',
      );
    }

    const cipherAlgorithm = 'aes-256-cbc';

    // Decode the base64 secret key and ensure it's 32 bytes for AES-256
    let keyBuffer: Buffer;
    try {
      keyBuffer = Buffer.from(secretKey, 'base64');
      if (keyBuffer.length !== 32) {
        throw new Error(
          `SECRET_KEY must be exactly 32 bytes when base64 decoded. Current length: ${keyBuffer.length} bytes`,
        );
      }
    } catch (error) {
      throw new Error(
        `Invalid SECRET_KEY format. Must be a valid base64-encoded 32-byte key. Error: ${error}`,
      );
    }

    const iv = randomBytes(16); // initialization vector
    const cipher = createCipheriv(
      cipherAlgorithm, // NOSONAR
      keyBuffer,
      iv,
    );

    let encrypted = cipher.update(password, 'utf-8', 'hex');
    encrypted += cipher.final('hex');

    return `${iv.toString('hex')}:${encrypted}`;
  }

  /**
   * Generate HMAC-SHA256 signature
   * @param key - secret key (Tenant secret)
   * @param message - message to sign(Tenant id)
   * @returns hex string of the signature
   */
  generateHmacSHA256(key: string, message: string): string {
    return createHmac('sha256', key).update(message).digest('hex');
  }

  /**
   * Decrypts a password that was encrypted using encryptPassword method.
   * @param {string} encryptedPassword - The encrypted password in the format "iv:encryptedData".
   * @returns {string} The decrypted password.
   * @throws {Error} If SECRET_KEY is not defined in environment variables or decryption fails.
   */
  decryptPassword(encryptedPassword: string): string {
    const secretKey = process.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error(
        'SECRET_KEY is not defined in the environment variables.',
      );
    }

    const [ivHex, encryptedData] = encryptedPassword.split(':');
    if (!ivHex || !encryptedData) {
      throw new Error(
        'Invalid encrypted password format. Expected format: "iv:encryptedData"',
      );
    }

    // Decode the base64 secret key and ensure it's 32 bytes for AES-256
    let keyBuffer: Buffer;
    try {
      keyBuffer = Buffer.from(secretKey, 'base64');
      if (keyBuffer.length !== 32) {
        throw new Error(
          `SECRET_KEY must be exactly 32 bytes when base64 decoded. Current length: ${keyBuffer.length} bytes`,
        );
      }
    } catch (error) {
      throw new Error(
        `Invalid SECRET_KEY format. Must be a valid base64-encoded 32-byte key. Error: ${error}`,
      );
    }

    const iv = Buffer.from(ivHex, 'hex');
    const decipher = createDecipheriv('aes-256-cbc', keyBuffer, iv);

    let decrypted = decipher.update(encryptedData, 'hex', 'utf-8');
    decrypted += decipher.final('utf-8');

    return decrypted;
  }
}
