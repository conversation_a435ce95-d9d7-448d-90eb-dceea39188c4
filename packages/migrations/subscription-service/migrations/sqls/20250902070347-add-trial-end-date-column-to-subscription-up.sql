/* Replace with your SQL commands */
ALTER TABLE main.subscriptions
  ADD COLUMN trial_end_date timestamptz;
ALTER TABLE main.plans
DROP CONSTRAINT IF EXISTS chk_plan_price_valid;

ALTER TABLE main.plans
ADD CONSTRAINT chk_plan_price_valid
CHECK (
  price >= 0 AND price = round(price::numeric, 2)
);


  ALTER TABLE main.plans
ALTER COLUMN billing_cycle_id DROP NOT NULL;


INSERT INTO main.plans (
  name,
  price,
  configure_devices_id,
  tier,
  billing_cycle_id,
  currency_id,
  created_on,
  modified_on,
  deleted,
  created_by,
  plan_size_id,
  status,
  version,
  meta_data
)
VALUES (
  'Free',
  0,
  (SELECT id FROM main.configure_devices WHERE min = 1 AND max = 4 LIMIT 1),
  'premium',
  NULL,
  (SELECT id FROM main.currencies WHERE currency_code = 'USD' LIMIT 1),
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP,
  false,
  '123e4567-e89b-12d3-a456-426614174002',
  (SELECT id FROM main.plan_sizes WHERE size = 'SMALL' LIMIT 1),
  2,
  'v1',
  '{"trialDays":"14"}'::jsonb
);



