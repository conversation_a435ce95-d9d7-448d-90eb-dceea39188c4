CREATE TABLE main.software_level (
    id uuid DEFAULT (md5(((random())::text || (clock_timestamp())::text)))::uuid NOT NULL,
    level varchar(100) NOT NULL,
    created_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by uuid,
    modified_by uuid,
    deleted boolean DEFAULT FALSE NOT NULL,
    deleted_on timestamptz,
    deleted_by uuid,
    CONSTRAINT pk_software_level_id PRIMARY KEY (id)
);

ALTER TABLE main.plans
ADD COLUMN software_level_id uuid,
ADD CONSTRAINT fk_plans_software_level
    FOREIGN KEY (software_level_id)
    REFERENCES main.software_level(id)
    ON DELETE SET NULL
    ON UPDATE CASCADE;



INSERT INTO main.software_level (
  level, created_on, modified_on, deleted, created_by
)
VALUES
  (
    'Historian',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    false,
    '123e4567-e89b-12d3-a456-************' -- Replace with actual user ID
  ),
  (
    'Remote Control',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    false,
    '123e4567-e89b-12d3-a456-************' -- Replace with actual user ID
  ),
  (
    '21 CFR Part 11 Compliance',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    false,
    '123e4567-e89b-12d3-a456-************' -- Replace with actual user ID
  );
