/* Replace with your SQL commands */
/* Replace with your SQL commands */
INSERT INTO main.notification_templates(event_name, body, subject, notification_type)
VALUES (
  'trial_ending_soon',
  '<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Trial Ending Soon</title>
  <style>
    body {
      font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f4f6f8;
      margin: 0;
      padding: 0;
      color: #333333;
    }
    .container {
      max-width: 650px;
      margin: 40px auto;
      background: #ffffff;
      padding: 35px;
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.08);
    }
    h2 {
      color: #d9534f;
      font-size: 22px;
      text-align: center;
      margin-bottom: 25px;
    }
    .details {
      font-size: 15px;
      line-height: 1.6;
    }
    .details p {
      margin: 10px 0;
    }
    .highlight {
      font-weight: bold;
      color: #007bff;
    }
    .cta-button {
      display: inline-block;
      margin-top: 20px;
      padding: 12px 20px;
      background-color: #007bff;
      color: #ffffff !important;
      text-decoration: none;
      border-radius: 5px;
      font-size: 15px;
    }
    .footer {
      margin-top: 30px;
      font-size: 13px;
      color: #777777;
      text-align: center;
      border-top: 1px solid #eaeaea;
      padding-top: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Your Trial is Ending Soon</h2>
    <div class="details">
      <p>Hi </p>
      <p>Your  trial will end after <span class="highlight">{{daysLeft}}</span> days.</p>
      <p>To continue enjoying uninterrupted access to our services, please upgrade to a paid plan before your trial ends.</p>
      
    </div>
    <div class="footer">
      If you have any questions, feel free to contact our support team.<br>
      We’re excited to continue serving you!
    </div>
  </div>
</body>
</html>',
  'Your Trial is Ending Soon',
  1
);
