import {expect} from '@loopback/testlab';
import sinon from 'sinon';
import {PlanStatus} from '@local/core';
import {ManageDeviceLimitService} from '../../services/manage-device-limit.service';
import {
  ConfigureDeviceRepository,
  PlanRepository,
  SubscriptionRepository,
} from '../../repositories';
import {ConfigureDevice} from '../../models';

describe('ManageDeviceLimitService', () => {
  let service: ManageDeviceLimitService;
  let configureDeviceRepository: sinon.SinonStubbedInstance<ConfigureDeviceRepository>;
  let planRepository: sinon.SinonStubbedInstance<PlanRepository>;
  let subscriptionRepository: sinon.SinonStubbedInstance<SubscriptionRepository>;

  beforeEach(() => {
    // Create stubbed instances of repositories
    configureDeviceRepository = sinon.createStubInstance(
      ConfigureDeviceRepository,
    );
    planRepository = sinon.createStubInstance(PlanRepository);
    subscriptionRepository = sinon.createStubInstance(SubscriptionRepository);

    // Create service instance with mocked repositories
    service = new ManageDeviceLimitService(
      configureDeviceRepository,
      planRepository,
      subscriptionRepository,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('upsertDeviceBatch', () => {
    it('should create new device when no id is provided', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {min: 1, max: 10, computeSize: 'small'},
      ];
      configureDeviceRepository.create.resolves({
        id: 'new-device-id',
      } as ConfigureDevice);

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.true();
      expect(result.error).to.be.undefined();
      sinon.assert.calledOnce(configureDeviceRepository.create);
      sinon.assert.calledWith(configureDeviceRepository.create, devices[0]);
    });

    it('should create new device when device with id does not exist', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'non-existent-id', min: 1, max: 10},
      ];
      configureDeviceRepository.exists.resolves(false);
      configureDeviceRepository.create.resolves({
        id: 'new-device-id',
      } as ConfigureDevice);

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.true();
      sinon.assert.calledOnce(configureDeviceRepository.exists);
      sinon.assert.calledWith(
        configureDeviceRepository.exists,
        'non-existent-id',
      );
      sinon.assert.calledOnce(configureDeviceRepository.create);
    });

    it('should update existing device when no active plans exist', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'existing-id', min: 2, max: 20},
      ];
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([]); // No active plans
      configureDeviceRepository.updateById.resolves();

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.true();
      sinon.assert.calledOnce(configureDeviceRepository.exists);
      sinon.assert.calledOnce(planRepository.findAll);
      sinon.assert.calledWith(planRepository.findAll, {
        where: {configureDeviceId: 'existing-id', status: PlanStatus.ACTIVE},
        fields: {id: true},
      });
      sinon.assert.calledOnce(configureDeviceRepository.updateById);
      sinon.assert.calledWith(
        configureDeviceRepository.updateById,
        'existing-id',
        devices[0],
      );
    });

    it('should update existing device when active plans exist but no subscriptions', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'existing-id', min: 2, max: 20},
      ];
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([{id: 'plan-1'}, {id: 'plan-2'}] as any);
      subscriptionRepository.find.resolves([]); // No subscriptions
      configureDeviceRepository.updateById.resolves();

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.true();
      sinon.assert.calledOnce(subscriptionRepository.find);
      sinon.assert.calledWith(subscriptionRepository.find, {
        where: {planId: {inq: ['plan-1', 'plan-2']}},
        limit: 1,
      });
      sinon.assert.calledOnce(configureDeviceRepository.updateById);
    });

    it('should throw error when device has active plans with subscriptions', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'existing-id', min: 2, max: 20},
      ];
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([{id: 'plan-1'}] as any);
      subscriptionRepository.find.resolves([{id: 'sub-1'}] as any); // Has subscriptions

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.false();
      expect(result.error).to.equal(
        'Cannot update configure device as it is associated with a subscription',
      );
      sinon.assert.notCalled(configureDeviceRepository.updateById);
    });

    it('should delete device and associated plans when device.deleted is true', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'existing-id', deleted: true},
      ];
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([]); // No active plans
      planRepository.deleteAll.resolves({count: 2} as any);
      configureDeviceRepository.deleteById.resolves();

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.true();
      sinon.assert.calledOnce(planRepository.deleteAll);
      sinon.assert.calledWith(planRepository.deleteAll, {
        configureDeviceId: 'existing-id',
      });
      sinon.assert.calledOnce(configureDeviceRepository.deleteById);
      sinon.assert.calledWith(
        configureDeviceRepository.deleteById,
        'existing-id',
      );
      sinon.assert.notCalled(configureDeviceRepository.updateById);
    });

    it('should process multiple devices successfully', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {min: 1, max: 10}, // New device without id
        {id: 'existing-id', min: 2, max: 20}, // Update existing
      ];
      configureDeviceRepository.create.resolves({
        id: 'new-id',
      } as ConfigureDevice);
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([]);
      configureDeviceRepository.updateById.resolves();

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.true();
      sinon.assert.calledOnce(configureDeviceRepository.create);
      sinon.assert.calledOnce(configureDeviceRepository.exists);
      sinon.assert.calledOnce(configureDeviceRepository.updateById);
    });

    it('should handle repository errors gracefully', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [{min: 1, max: 10}];
      configureDeviceRepository.create.rejects(
        new Error('Database connection failed'),
      );

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.false();
      expect(result.error).to.equal('Database connection failed');
    });

    it('should handle unknown errors gracefully', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [{min: 1, max: 10}];
      configureDeviceRepository.create.rejects({}); // Simulate error without message

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.false();
      expect(result.error).to.equal('Unknown error');
    });

    it('should handle empty devices array', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [];

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.true();
      expect(result.error).to.be.undefined();
      sinon.assert.notCalled(configureDeviceRepository.create);
      sinon.assert.notCalled(configureDeviceRepository.exists);
      sinon.assert.notCalled(configureDeviceRepository.updateById);
    });

    it('should handle mixed success and failure scenarios', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {min: 1, max: 10}, // This will succeed
        {id: 'existing-with-subscription', min: 2, max: 20}, // This will fail
      ];

      // First device succeeds
      configureDeviceRepository.create
        .onFirstCall()
        .resolves({id: 'new-id'} as ConfigureDevice);

      // Second device fails due to subscription
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([{id: 'plan-1'}] as any);
      subscriptionRepository.find.resolves([{id: 'sub-1'}] as any);

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.false();
      expect(result.error).to.equal(
        'Cannot update configure device as it is associated with a subscription',
      );
    });

    it('should handle device deletion when subscriptions exist', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'existing-id', deleted: true},
      ];
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([{id: 'plan-1'}] as any);
      subscriptionRepository.find.resolves([{id: 'sub-1'}] as any); // Has subscriptions

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.false();
      expect(result.error).to.equal(
        'Cannot update configure device as it is associated with a subscription',
      );
      sinon.assert.notCalled(planRepository.deleteAll);
      sinon.assert.notCalled(configureDeviceRepository.deleteById);
    });

    it('should handle plan repository errors during active plan check', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'existing-id', min: 2, max: 20},
      ];
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.rejects(new Error('Plan repository error'));

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.false();
      expect(result.error).to.equal('Plan repository error');
    });

    it('should handle subscription repository errors during subscription check', async () => {
      // Arrange
      const devices: Partial<ConfigureDevice>[] = [
        {id: 'existing-id', min: 2, max: 20},
      ];
      configureDeviceRepository.exists.resolves(true);
      planRepository.findAll.resolves([{id: 'plan-1'}] as any);
      subscriptionRepository.find.rejects(
        new Error('Subscription repository error'),
      );

      // Act
      const result = await service.upsertDeviceBatch(devices);

      // Assert
      expect(result.success).to.be.false();
      expect(result.error).to.equal('Subscription repository error');
    });
  });
});
