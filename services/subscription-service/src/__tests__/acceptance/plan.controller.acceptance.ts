import {
  getEnumMap,
  PermissionKey,
  PlanStatus,
  PlanTierType,
  SubscriptionStatus,
} from '@local/core';
import {Client, expect} from '@loopback/testlab';
import {STATUS_CODE} from '@sourceloop/core';
import {SubscriptionServiceApplication} from '../../application';
import {Plan} from '../../models/plan.model';
import {PlanRepository, SubscriptionRepository} from '../../repositories';
import {getRepo, getToken, setupApplication} from './test-helper';
import {IBillingService} from '../../types';
import {StripeService} from '../../services';
import sinon from 'sinon';
import {BillingComponentBindings} from 'loopback4-billing';

describe('PlanController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let planRepository: PlanRepository;
  let billingProvider: sinon.SinonStubbedInstance<IBillingService>;

  const mockPlan: Plan = {
    name: 'Pro Plan',
    description: 'Test Plan',
    tier: PlanTierType.PREMIUM,
    price: 100,
    metaData: {feature: 'test'},
    status: PlanStatus.ACTIVE,
    version: 'v1',
    allowedUnlimitedUsers: true,
    costPerUser: 10,
    billingCycleId: 'bc-123',
    currencyId: 'cur-123',
    configureDeviceId: 'cd-123',
    planSizeId: 'ps-123',
    createdOn: new Date().toISOString(),
    modifiedOn: new Date().toISOString(),
    deleted: false,
    productRefId: 'prod-123',
  } as unknown as Plan;
  function getBillingStub() {
    return {
      createProduct: sinon.stub(),
      createPrice: sinon.stub(),
      updateSubscription: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<IBillingService>;
  }

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    planRepository = await getRepo(app, 'repositories.PlanRepository');

    billingProvider = getBillingStub();
    billingProvider.createProduct.resolves('mock-product-id');
    app.bind(BillingComponentBindings.SDKProvider).to(billingProvider);
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });
  async function seedData() {
    await planRepository.create({
      ...mockPlan,
    });
  }

  afterEach(async () => {
    await planRepository.deleteAllHard();
    sinon.restore();
  });

  it('POST /plans - creates a plan with valid token', async () => {
    const token = getToken([PermissionKey.CreatePlan]);
    await client
      .post('/plans')
      .set('Authorization', token)
      .send({
        name: mockPlan.name,
        tier: mockPlan.tier,
        price: mockPlan.price,
        version: mockPlan.version,
        billingCycleId: mockPlan.billingCycleId,
        currencyId: mockPlan.currencyId,
        configureDeviceId: mockPlan.configureDeviceId,
        planSizeId: mockPlan.planSizeId,
      })
      .expect(STATUS_CODE.OK);
  });

  it('GET /plans/count - returns count with valid token', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const {body} = await client
      .get('/plans/count')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count');
  });

  it('GET /plans - returns all plans with valid token', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const {body} = await client
      .get('/plans')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.Array();
    expect(body[0]).to.containEql({
      name: mockPlan.name,
      tier: mockPlan.tier,
    });
  });

  it('GET /plans/{id} - returns a plan by ID with valid token', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const plan = await planRepository.findOne();
    const {body} = await client
      .get(`/plans/${plan?.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.containEql({
      name: mockPlan.name,
      tier: mockPlan.tier,
    });
  });

  it('PATCH /plans/{id} - updates a single plan by ID with valid token', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);
    const plan = await planRepository.findOne();
    await client
      .patch(`/plans/${plan?.id}`)
      .set('Authorization', token)
      .send({price: 250})
      .expect(STATUS_CODE.NO_CONTENT);

    const updated = await planRepository.findById(plan!.id);
    expect(updated.price).to.equal(250);
  });

  it('PUT /plans/{id} - replaces a plan by ID with valid token', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);
    const plan = await planRepository.findOne();
    const newPlanData = {
      ...mockPlan,
      name: 'Replaced Plan',
      price: 300,
    };
    await client
      .put(`/plans/${plan?.id}`)
      .set('Authorization', token)
      .send(newPlanData)
      .expect(STATUS_CODE.NO_CONTENT);

    const replaced = await planRepository.findById(plan!.id);
    expect(replaced.name).to.equal('Replaced Plan');
    expect(replaced.price).to.equal(300);
  });

  it('DELETE /plans/{id} - deletes a plan by ID with valid token', async () => {
    const token = getToken([PermissionKey.DeletePlan]);
    const plan = await planRepository.findOne();
    await client
      .del(`/plans/${plan?.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.NO_CONTENT);

    const found = await planRepository.findById(plan!.id).catch(err => err);
    expect(found).to.have.property('statusCode', 404);
  });

  it('POST /plans - should handle unique constraint error', async () => {
    const token = getToken([PermissionKey.CreatePlan]);
    // Force billingProvider to succeed
    billingProvider.createProduct.resolves('mock-product-id');

    // First plan created
    await client
      .post('/plans')
      .set('Authorization', token)
      .send({
        name: mockPlan.name,
        tier: mockPlan.tier,
        price: mockPlan.price,
        version: mockPlan.version,
        billingCycleId: mockPlan.billingCycleId,
        currencyId: mockPlan.currencyId,
        configureDeviceId: mockPlan.configureDeviceId,
        planSizeId: mockPlan.planSizeId,
      })
      .expect(STATUS_CODE.OK);

    // Simulate DB throwing constraint violation on duplicate
    sinon.restore();
    billingProvider = sinon.createStubInstance<IBillingService>(StripeService);
    billingProvider.createProduct.rejects({
      constraint: 'unique_plan_combination',
    });
    app.bind(BillingComponentBindings.SDKProvider).to(billingProvider);

    await client
      .post('/plans')
      .set('Authorization', token)
      .send({
        name: mockPlan.name,
        tier: mockPlan.tier,
        price: mockPlan.price,
        version: mockPlan.version,
        billingCycleId: mockPlan.billingCycleId,
        currencyId: mockPlan.currencyId,
        configureDeviceId: mockPlan.configureDeviceId,
        planSizeId: mockPlan.planSizeId,
      })
      .expect(STATUS_CODE.BAD_REQUEST);
  });

  it('GET /plans/{id} - returns 404 for non-existing plan', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    await client
      .get('/plans/non-existing-id')
      .set('Authorization', token)
      .expect(STATUS_CODE.NOT_FOUND);
  });

  it('PUT /plans/{id} - returns 404 if plan does not exist', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);
    await client
      .put('/plans/non-existing-id')
      .set('Authorization', token)
      .send({...mockPlan, name: 'Missing Plan'})
      .expect(STATUS_CODE.NOT_FOUND);
  });

  it('DELETE /plans/{id} - returns 404 if plan does not exist', async () => {
    const token = getToken([PermissionKey.DeletePlan]);
    await client
      .del('/plans/non-existing-id')
      .set('Authorization', token)
      .expect(STATUS_CODE.NOT_FOUND);
  });

  it('POST /plans - returns 401 if no token provided', async () => {
    await client
      .post('/plans')
      .send({
        name: 'Unauthorized Plan',
        tier: mockPlan.tier,
        price: mockPlan.price,
        version: mockPlan.version,
        billingCycleId: mockPlan.billingCycleId,
        currencyId: mockPlan.currencyId,
        configureDeviceId: mockPlan.configureDeviceId,
        planSizeId: mockPlan.planSizeId,
      })
      .expect(STATUS_CODE.UNAUTHORISED);
  });

  it('POST /plans - returns 403 if user lacks permission', async () => {
    const token = getToken([]); // no CreatePlan permission
    await client
      .post('/plans')
      .set('Authorization', token)
      .send({
        name: 'Forbidden Plan',
        tier: mockPlan.tier,
        price: mockPlan.price,
        version: mockPlan.version,
        billingCycleId: mockPlan.billingCycleId,
        currencyId: mockPlan.currencyId,
        configureDeviceId: mockPlan.configureDeviceId,
        planSizeId: mockPlan.planSizeId,
      })
      .expect(STATUS_CODE.FORBIDDEN);
  });

  it('PATCH /plans/{id} - should not deactivate plan with active subscriptions', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);
    const plan = await planRepository.findOne();

    // Mock subscriptionRepository to simulate active subscriptions
    const subscriptionRepo: SubscriptionRepository = await getRepo(
      app,
      'repositories.SubscriptionRepository',
    );
    await subscriptionRepo.create({
      planId: plan!.id,
      status: SubscriptionStatus.ACTIVE,
      externalSubscriptionId: 'ext-sub-123',
      subscriberId: 'subs-124',
      totalCost: 100,
      priceRefId: 'price-123',
    });

    await client
      .patch(`/plans/${plan?.id}`)
      .set('Authorization', token)
      .send({status: PlanStatus.INACTIVE})
      .expect(STATUS_CODE.BAD_REQUEST);
  });

  it('PATCH /plans - updates multiple plans with valid token', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);
    const {body} = await client
      .patch('/plans')
      .set('Authorization', token)
      .send({price: 200})
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });

  it('GET /plans/all-status - returns all plan statuses with valid token', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const {body} = await client
      .get('/plans/all-status')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    const expectedStatuses = getEnumMap(PlanStatus);
    expect(body).to.deepEqual({statuses: expectedStatuses});
  });
  it('GET /plans/count with filter', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const {body} = await client
      .get('/plans/count')
      .query({where: {status: PlanStatus.ACTIVE}})
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });
});
