import sinon from 'sinon';
import {Client, expect} from '@loopback/testlab';
import {ConfigureDeviceRepository} from '../../repositories';
import {getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {SubscriptionServiceApplication} from '../../application';
import {ConfigureDevice} from '../../models';
import {PermissionKey} from '@local/core';

describe('ConfigureDeviceController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let configureDeviceRepository: sinon.SinonStubbedInstance<ConfigureDeviceRepository>;

  const mockConfigureDevice: ConfigureDevice = {
    id: '12345',
    min: 10,
    max: 100,
    createdOn: new Date().toISOString(),
    modifiedOn: new Date().toISOString(),
    deleted: false,
  } as unknown as ConfigureDevice;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    configureDeviceRepository = {
      create: sinon.stub().resolves(mockConfigureDevice),
      find: sinon.stub().resolves([mockConfigureDevice]),
      updateAll: sinon.stub().resolves({count: 1}),
      deleteById: sinon.stub().resolves(),
    } as unknown as sinon.SinonStubbedInstance<ConfigureDeviceRepository>;

    app
      .bind('repositories.ConfigureDeviceRepository')
      .to(configureDeviceRepository);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('invokes POST /configure-devices with valid token', async () => {
    const token = getToken([PermissionKey.CreateConfigureDevices]);
    const {body} = await client
      .post('/configure-devices')
      .set('Authorization', token)
      .send({
        min: 10,
        max: 100,
      })
      .expect(STATUS_CODE.OK);

    expect(body).to.containEql({
      id: mockConfigureDevice.id,
      min: mockConfigureDevice.min,
      max: mockConfigureDevice.max,
    });
  });

  it('invokes GET /configure-devices with valid token', async () => {
    const token = getToken([PermissionKey.ViewConfigureDevices]);
    const {body} = await client
      .get('/configure-devices')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.Array();
    expect(body[0]).to.containEql({
      id: mockConfigureDevice.id,
      min: mockConfigureDevice.min,
      max: mockConfigureDevice.max,
    });
  });

  it('invokes PATCH /configure-devices with valid token', async () => {
    const token = getToken([PermissionKey.UpdateConfigureDevices]);
    const {body} = await client
      .patch('/configure-devices')
      .set('Authorization', token)
      .send({max: 200})
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });

  it('invokes DELETE /configure-devices/{id} with valid token', async () => {
    const token = getToken([PermissionKey.DeleteConfigureDevices]);
    await client
      .del(`/configure-devices/${mockConfigureDevice.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.NO_CONTENT);
  });

  describe('POST /configure-devices/upsert-batch', () => {
    it('invokes POST /configure-devices/upsert-batch with valid token', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {min: 1, max: 10},
        {min: 5, max: 50},
      ];

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
    });

    it('should return 401 when no authorization token provided', async () => {
      const devices = [{min: 1, max: 10}];

      await client
        .post('/configure-devices/upsert-batch')
        .send(devices)
        .expect(STATUS_CODE.UNAUTHORISED);
    });

    it('should return 403 when insufficient permissions', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]); // Wrong permission
      const devices = [{min: 1, max: 10}];

      await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.FORBIDDEN);
    });
  });
});
