import {PermissionKey} from '@local/core';
import {Client, expect} from '@loopback/testlab';
import {STATUS_CODE} from '@sourceloop/core';
import {SubscriptionServiceApplication} from '../../application';
import {SoftwareLevelRepository} from '../../repositories';
import {getRepo, getToken, setupApplication} from './test-helper';

describe('SoftwareLevelController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;

  let softwareLevelRepository: SoftwareLevelRepository;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    softwareLevelRepository = await getRepo(
      app,
      'repositories.SoftwareLevelRepository',
    );
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });

  afterEach(async () => {
    await softwareLevelRepository.deleteAllHard();
  });

  it('GET /software-level - returns all software levels with valid token', async () => {
    const token = getToken([PermissionKey.ViewSoftwareLevel]);
    const {body} = await client
      .get('/software-level')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.Array();
    expect(body[0]).to.containEql({
      level: 'Historian',
    });
  });

  it('GET /software-level - throws not allowed if token has improper permission', async () => {
    const token = getToken([PermissionKey.ViewAdminSettings]);
    await client
      .get('/software-level')
      .set('Authorization', token)
      .expect(STATUS_CODE.FORBIDDEN);
  });

  async function seedData() {
    // create a billing customer in repo
    await softwareLevelRepository.create({level: 'Historian'});
  }
});
