import {Client, expect} from '@loopback/testlab';
import sinon from 'sinon';
import {SubscriptionServiceApplication} from '../../application';
import {getRepo, getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {BillingComponentBindings} from 'loopback4-billing';
import {IBillingService} from '../../types';
import {StripeService} from '../../services';
import {BillingCustomerRepository, InvoiceRepository} from '../../repositories';
import {mockBillingCustomer} from './mock-data';
import {
  AddressDto,
  ChargeDto,
  InvoiceDto,
} from '@sourceloop/ctrl-plane-subscription-service';
import {PermissionKey} from '@local/core';

const basePath = '/billing-invoice';

describe('BillingInvoiceController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;

  let billingCustomerRepo: BillingCustomerRepository;
  let invoiceRepo: InvoiceRepository;

  let billingProvider: sinon.SinonStubbedInstance<IBillingService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());

    billingCustomerRepo = await getRepo(
      app,
      'repositories.BillingCustomerRepository',
    );
    invoiceRepo = await getRepo(app, 'repositories.InvoiceRepository');

    // Mock provider
    billingProvider = sinon.createStubInstance<IBillingService>(StripeService);
    billingProvider.createInvoice.resolves(
      new InvoiceDto({
        id: 'inv_123',
        customerId: 'cus_123',
        status: 'paid',
        currencyCode: 'INR',
        charges: [new ChargeDto({amount: 1000, description: 'Test charge'})],
        shippingAddress: new AddressDto({
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          city: 'Bangalore',
          state: 'KA',
          zip: '560001',
          country: 'IN',
        }),
        options: {autoAdvance: true},
      }),
    );

    app.bind(BillingComponentBindings.SDKProvider).to(billingProvider);
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });

  afterEach(async () => {
    await billingCustomerRepo.deleteAllHard();
    await invoiceRepo.deleteAllHard();
  });

  it('should create an invoice on POST /billing-invoice', async () => {
    const token = getToken([PermissionKey.CreateBillingInvoice]);

    const payload = {
      customerId: 'cus_123',
      currencyCode: 'INR',
      options: {autoAdvance: true},
      shippingAddress: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        city: 'Bangalore',
        state: 'KA',
        zip: '560001',
        country: 'IN',
      },
    };

    const res = await client
      .post(basePath)
      .set('Authorization', token)
      .send(payload)
      .expect(STATUS_CODE.OK);

    // Response validation
    expect(res.body).to.have.property('id', 'inv_123');
    expect(res.body).to.have.property('customerId', 'cus_123');
    expect(res.body).to.have.property('status', 'paid');
    expect(res.body.currencyCode).to.equal('INR');
    expect(res.body.shippingAddress).to.containEql({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      city: 'Bangalore',
      state: 'KA',
      zip: '560001',
      country: 'IN',
    });

    // Provider should be called
    sinon.assert.calledOnce(billingProvider.createInvoice);
    sinon.assert.calledWithMatch(
      billingProvider.createInvoice as sinon.SinonStub,
      payload,
    );
  });

  it('should not allow POST /billing-invoice without token', async () => {
    const payload = {
      customerId: 'cus_123',
      currencyCode: 'INR',
    };

    await client.post(basePath).send(payload).expect(STATUS_CODE.UNAUTHORISED);
  });

  async function seedData() {
    // create a billing customer in repo
    await billingCustomerRepo.create({
      ...mockBillingCustomer,
      customerId: 'cus_123',
    });
  }
});
