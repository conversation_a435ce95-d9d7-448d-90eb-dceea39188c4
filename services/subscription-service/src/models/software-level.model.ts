import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

/**
 * Model representing a software level.
 *
 * @remarks
 * A software level defines tiers or levels of software usage, which
 * can be associated with multiple plans. It extends UserModifiableEntity,
 * so it includes auditing fields like createdBy, modifiedBy, createdAt,
 * and modifiedAt.
 *
 * @property {string} id - Unique identifier for the software level (auto-generated).
 * @property {string} name - Name of the software level (required).
 * @property {boolean} deleted - Soft delete flag (default: false).
 */
@model({
  name: 'software_level',
})
export class SoftwareLevel extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      maxLength: 100,
    },
  })
  level: string;

  constructor(data?: Partial<SoftwareLevel>) {
    super(data);
  }
}
