{"openapi": "3.0.0", "info": {"title": "subscription-service", "version": "1.0.0", "description": "subscription-service", "contact": {"name": "<PERSON><PERSON>-<PERSON>", "email": "<EMAIL>"}}, "paths": {"/billing-customer/{tenantId}": {"patch": {"x-controller-name": "BillingCustomerController", "x-operation-name": "updateById", "tags": ["BillingCustomerController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "BillingCustomer PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 5327   |\n", "parameters": [{"name": "tenantId", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "BillingCustomerController.updateById"}, "delete": {"x-controller-name": "BillingCustomerController", "x-operation-name": "deleteById", "tags": ["BillingCustomerController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "BillingCustomer DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 5331   |\n", "parameters": [{"name": "tenantId", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "BillingCustomerController.deleteById"}}, "/billing-customer": {"post": {"x-controller-name": "BillingCustomerController", "x-operation-name": "create", "tags": ["BillingCustomerController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "BillingCustomer model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewBillingCustomer"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 5321   |\n", "parameters": [{"name": "tenantId", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewCustomer"}}}}, "operationId": "BillingCustomerController.create"}, "get": {"x-controller-name": "BillingCustomerController", "x-operation-name": "getCustomer", "tags": ["BillingCustomerController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "BillingCustomer model ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 5324   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/billing_customer.Filter"}}}}], "operationId": "BillingCustomerController.getCustomer"}}, "/billing-cycles/count": {"get": {"x-controller-name": "BillinCycleController", "x-operation-name": "count", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "BillingCycle model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7024   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "billing_cycles.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<BillingCycle>"}}}}], "operationId": "BillinCycleController.count"}}, "/billing-cycles/{id}": {"put": {"x-controller-name": "BillinCycleController", "x-operation-name": "replaceById", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "BillingCycle PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7022   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingCycle"}}}, "x-parameter-index": 1}, "operationId": "BillinCycleController.replaceById"}, "patch": {"x-controller-name": "BillinCycleController", "x-operation-name": "updateById", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "BillingCycle PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7022   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingCyclePartial"}}}, "x-parameter-index": 1}, "operationId": "BillinCycleController.updateById"}, "get": {"x-controller-name": "BillinCycleController", "x-operation-name": "findById", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "BillingCycle model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingCycleWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7024   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/billing_cycles.Filter"}}}}], "operationId": "BillinCycleController.findById"}, "delete": {"x-controller-name": "BillinCycleController", "x-operation-name": "deleteById", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "BillingCycle DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7023   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "BillinCycleController.deleteById"}}, "/billing-cycles": {"post": {"x-controller-name": "BillinCycleController", "x-operation-name": "create", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "BillingCycle model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingCycle"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7021   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewBillingCycle"}}}}, "operationId": "BillinCycleController.create"}, "patch": {"x-controller-name": "BillinCycleController", "x-operation-name": "updateAll", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "BillingCycle PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7022   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "billing_cycles.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<BillingCycle>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingCyclePartial"}}}}, "operationId": "BillinCycleController.updateAll"}, "get": {"x-controller-name": "BillinCycleController", "x-operation-name": "find", "tags": ["BillinCycleController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of BillingCycle model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BillingCycleWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7024   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/billing_cycles.Filter1"}}}}], "operationId": "BillinCycleController.find"}}, "/billing-invoice": {"post": {"x-controller-name": "BillingInvoiceController", "x-operation-name": "create", "tags": ["BillingInvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "invoice model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 5323   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/newInvoice"}}}}, "operationId": "BillingInvoiceController.create"}}, "/billing-payment-source/{paymentSourceId}": {"delete": {"x-controller-name": "BillingPaymentSourceController", "x-operation-name": "deleteById", "tags": ["BillingPaymentSourceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Billing Payment Source DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 5332   |\n", "parameters": [{"name": "paymentSourceId", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "BillingPaymentSourceController.deleteById"}}, "/billing-payment-source": {"post": {"x-controller-name": "BillingPaymentSourceController", "x-operation-name": "create", "tags": ["BillingPaymentSourceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Payment model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSourceDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 5322   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPaymentSource"}}}}, "operationId": "BillingPaymentSourceController.create"}, "get": {"x-controller-name": "BillingPaymentSourceController", "x-operation-name": "getPaymentSource", "tags": ["BillingPaymentSourceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "get payment source", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSourceDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 5325   |\n", "parameters": [{"name": "paymentSourceId", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "BillingPaymentSourceController.getPaymentSource"}}, "/configure-devices/upsert-batch": {"post": {"x-controller-name": "ConfigureDeviceController", "x-operation-name": "upsertBatch", "tags": ["ConfigureDeviceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Upsert batch result", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "error": {"type": "string"}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6025   |\n", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigureDevicePartial"}}}}}, "operationId": "ConfigureDeviceController.upsertBatch"}}, "/configure-devices/{id}": {"delete": {"x-controller-name": "ConfigureDeviceController", "x-operation-name": "deleteById", "tags": ["ConfigureDeviceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "ConfigureDevice DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 6026   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "ConfigureDeviceController.deleteById"}}, "/configure-devices": {"post": {"x-controller-name": "ConfigureDeviceController", "x-operation-name": "create", "tags": ["ConfigureDeviceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "ConfigureDevice model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigureDevice"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6023   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewConfigureDevice"}}}}, "operationId": "ConfigureDeviceController.create"}, "patch": {"x-controller-name": "ConfigureDeviceController", "x-operation-name": "updateAll", "tags": ["ConfigureDeviceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "ConfigureDevice PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6025   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "configure_devices.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<ConfigureDevice>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfigureDevicePartial"}}}}, "operationId": "ConfigureDeviceController.updateAll"}, "get": {"x-controller-name": "ConfigureDeviceController", "x-operation-name": "find", "tags": ["ConfigureDeviceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of ConfigureDevice model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigureDeviceWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6024   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/configure_devices.Filter"}}}}], "operationId": "ConfigureDeviceController.find"}}, "/currencies/count": {"get": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "count", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Currency model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7028   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "currencies.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Currency>"}}}}], "operationId": "CurrencyController.count"}}, "/currencies/{id}": {"put": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "replaceById", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Currency PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7026   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Currency"}}}, "x-parameter-index": 1}, "operationId": "CurrencyController.replaceById"}, "patch": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "updateById", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Currency PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7026   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyPartial"}}}, "x-parameter-index": 1}, "operationId": "CurrencyController.updateById"}, "get": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "findById", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Currency model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7028   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/currencies.Filter"}}}}], "operationId": "CurrencyController.findById"}, "delete": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "deleteById", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Currency DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7027   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "CurrencyController.deleteById"}}, "/currencies": {"post": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "create", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Currency model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Currency"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7025   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewCurrency"}}}}, "operationId": "CurrencyController.create"}, "patch": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "updateAll", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Currency PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7026   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "currencies.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Currency>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyPartial"}}}}, "operationId": "CurrencyController.updateAll"}, "get": {"x-controller-name": "C<PERSON><PERSON>cyController", "x-operation-name": "find", "tags": ["C<PERSON><PERSON>cyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Currency model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7028   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/currencies.Filter1"}}}}], "operationId": "CurrencyController.find"}}, "/feature-values/count": {"get": {"x-controller-name": "FeatureValuesController", "x-operation-name": "count", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "FeatureValues model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewFeatureValues   |\n| 9   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "feature_values.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<FeatureValues>"}}}}], "operationId": "FeatureValuesController.count"}}, "/feature-values/{id}": {"put": {"x-controller-name": "FeatureValuesController", "x-operation-name": "replaceById", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "FeatureValues PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateFeatureValues   |\n| 11   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureValues"}}}, "x-parameter-index": 1}, "operationId": "FeatureValuesController.replaceById"}, "patch": {"x-controller-name": "FeatureValuesController", "x-operation-name": "updateById", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "FeatureValues PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateFeatureValues   |\n| 11   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureValuesPartial"}}}, "x-parameter-index": 1}, "operationId": "FeatureValuesController.updateById"}, "get": {"x-controller-name": "FeatureValuesController", "x-operation-name": "findById", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "FeatureValues model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureValuesWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewFeatureValues   |\n| 9   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/feature_values.Filter"}}}}], "operationId": "FeatureValuesController.findById"}, "delete": {"x-controller-name": "FeatureValuesController", "x-operation-name": "deleteById", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "FeatureValues DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteFeatureValues   |\n| 12   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "FeatureValuesController.deleteById"}}, "/feature-values": {"post": {"x-controller-name": "FeatureValuesController", "x-operation-name": "create", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "FeatureValues model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureValues"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateFeatureValues   |\n| 10   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewFeatureValues"}}}}, "operationId": "FeatureValuesController.create"}, "patch": {"x-controller-name": "FeatureValuesController", "x-operation-name": "updateAll", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "FeatureValues PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateFeatureValues   |\n| 11   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "feature_values.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<FeatureValues>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureValuesPartial"}}}}, "operationId": "FeatureValuesController.updateAll"}, "get": {"x-controller-name": "FeatureValuesController", "x-operation-name": "find", "tags": ["FeatureValuesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of FeatureValues model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FeatureValuesWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewFeatureValues   |\n| 9   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/feature_values.Filter1"}}}}], "operationId": "FeatureValuesController.find"}}, "/features/count": {"get": {"x-controller-name": "FeatureController", "x-operation-name": "count", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Feature model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewFeature   |\n| 1   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "features.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Feature>"}}}}], "operationId": "FeatureController.count"}}, "/features/{id}": {"put": {"x-controller-name": "FeatureController", "x-operation-name": "replaceById", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Feature PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateFeature   |\n| 3   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Feature"}}}, "x-parameter-index": 1}, "operationId": "FeatureController.replaceById"}, "patch": {"x-controller-name": "FeatureController", "x-operation-name": "updateById", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Feature PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateFeature   |\n| 3   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeaturePartial"}}}, "x-parameter-index": 1}, "operationId": "FeatureController.updateById"}, "get": {"x-controller-name": "FeatureController", "x-operation-name": "findById", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Feature model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewFeature   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/features.Filter"}}}}], "operationId": "FeatureController.findById"}, "delete": {"x-controller-name": "FeatureController", "x-operation-name": "deleteById", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Feature DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteFeature   |\n| 4   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "FeatureController.deleteById"}}, "/features": {"post": {"x-controller-name": "FeatureController", "x-operation-name": "create", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Feature model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Feature"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateFeature   |\n| 2   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewFeature"}}}}, "operationId": "FeatureController.create"}, "patch": {"x-controller-name": "FeatureController", "x-operation-name": "updateAll", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Feature PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateFeature   |\n| 3   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "features.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Feature>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeaturePartial"}}}}, "operationId": "FeatureController.updateAll"}, "get": {"x-controller-name": "FeatureController", "x-operation-name": "find", "tags": ["FeatureController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Feature model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FeatureWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewFeature   |\n| 1   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/features.Filter1"}}}}], "operationId": "FeatureController.find"}}, "/invoices/count": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "count", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Invoice model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "invoice.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Invoice>"}}}}], "operationId": "InvoiceController.count"}}, "/invoices/last-unpaid": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "getLastUnpaidInvoicePaymentLink", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Last unpaid invoice payment link", "content": {"application/json": {"schema": {"type": "string", "nullable": true}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "tenantId", "in": "query", "schema": {"type": "string"}}], "operationId": "InvoiceController.getLastUnpaidInvoicePaymentLink"}}, "/invoices/{id}/pdf-url": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "getStripeInvoicePdfUrl", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Stripe Invoice PDF URL", "content": {"application/json": {"schema": {"type": "object", "properties": {"pdfUrl": {"type": "string", "nullable": true}}}}}}, "400": {"description": "Missing stripeInvoiceId in request body or filter"}}, "description": "\n\n| Permissions |\n| ------- |\n| DownloadInvoice   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "stripeInvoiceId", "in": "query", "schema": {"type": "string"}}], "operationId": "InvoiceController.getStripeInvoicePdfUrl"}}, "/invoices/{id}": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "findById", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Invoice model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/invoice.Filter"}}}}], "operationId": "InvoiceController.findById"}}, "/invoices": {"get": {"x-controller-name": "InvoiceController", "x-operation-name": "find", "tags": ["InvoiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of ConfigureDevice model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/invoice.Filter1"}}}}], "operationId": "InvoiceController.find"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "description": "", "operationId": "PingController.ping"}}, "/plan-histories/{id}": {"delete": {"x-controller-name": "PlanHistoryController", "x-operation-name": "deleteById", "tags": ["PlanHistoryController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan.PlanHistory DELETE success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6022   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "PlanHistoryController.deleteById"}}, "/plan-histories": {"post": {"x-controller-name": "PlanHistoryController", "x-operation-name": "create", "tags": ["PlanHistoryController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanHistory"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6019   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPlanHistoryInPlan"}}}}, "operationId": "PlanHistoryController.create"}, "patch": {"x-controller-name": "PlanHistoryController", "x-operation-name": "patch", "tags": ["PlanHistoryController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan.PlanHistory PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6021   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "plan_history.Where<PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<PlanHistory>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanHistoryPartial"}}}}, "operationId": "PlanHistoryController.patch"}, "get": {"x-controller-name": "PlanHistoryController", "x-operation-name": "find", "tags": ["PlanHistoryController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Plan has many PlanHistory", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanHistory"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6020   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "PlanHistoryController.find"}}, "/plan-sizes/count": {"get": {"x-controller-name": "PlanSizesController", "x-operation-name": "count", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "PlanSizes model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7029   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "plan_sizes.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<PlanSizes>"}}}}], "operationId": "PlanSizesController.count"}}, "/plan-sizes/{id}": {"put": {"x-controller-name": "PlanSizesController", "x-operation-name": "replaceById", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "PlanSizes PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7031   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanSizes"}}}, "x-parameter-index": 1}, "operationId": "PlanSizesController.replaceById"}, "patch": {"x-controller-name": "PlanSizesController", "x-operation-name": "updateById", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "PlanSizes PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7031   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanSizesPartial"}}}, "x-parameter-index": 1}, "operationId": "PlanSizesController.updateById"}, "get": {"x-controller-name": "PlanSizesController", "x-operation-name": "findById", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "PlanSizes model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanSizesWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7029   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plan_sizes.Filter"}}}}], "operationId": "PlanSizesController.findById"}, "delete": {"x-controller-name": "PlanSizesController", "x-operation-name": "deleteById", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "PlanSizes DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7032   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "PlanSizesController.deleteById"}}, "/plan-sizes": {"post": {"x-controller-name": "PlanSizesController", "x-operation-name": "create", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "PlanSizes model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanSizes"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7030   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPlanSizes"}}}}, "operationId": "PlanSizesController.create"}, "patch": {"x-controller-name": "PlanSizesController", "x-operation-name": "updateAll", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "PlanSizes PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7031   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "plan_sizes.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<PlanSizes>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanSizesPartial"}}}}, "operationId": "PlanSizesController.updateAll"}, "get": {"x-controller-name": "PlanSizesController", "x-operation-name": "find", "tags": ["PlanSizesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of PlanSizes model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanSizesWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7029   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plan_sizes.Filter1"}}}}], "operationId": "PlanSizesController.find"}}, "/plans/all-status": {"get": {"x-controller-name": "PlanController", "x-operation-name": "findAllStatus", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object of all possible tenant status", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "PlanController.findAllStatus"}}, "/plans/count": {"get": {"x-controller-name": "PlanController", "x-operation-name": "count", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7008   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "plans.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Plan>"}}}}], "operationId": "PlanController.count"}}, "/plans/{id}/features": {"post": {"x-controller-name": "PlanFeaturesController", "x-operation-name": "setPlanFeatures", "tags": ["PlanFeaturesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan Features Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureValues"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7034   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array"}}}, "x-parameter-index": 1}, "operationId": "PlanFeaturesController.setPlanFeatures"}, "patch": {"x-controller-name": "PlanFeaturesController", "x-operation-name": "updatePlanFeatures", "tags": ["PlanFeaturesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan Features Updated", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FeatureValues"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7035   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FeatureValuesPartial"}}}}, "x-parameter-index": 1}, "operationId": "PlanFeaturesController.updatePlanFeatures"}, "get": {"x-controller-name": "PlanFeaturesController", "x-operation-name": "getPlanFeatures", "tags": ["PlanFeaturesController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan Features Retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureValues"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7033   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "PlanFeaturesController.getPlanFeatures"}}, "/plans/{id}/subscriptions": {"get": {"x-controller-name": "PlanSubscriptionController", "x-operation-name": "find", "tags": ["PlanSubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7008   |\n| 7004   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "PlanSubscriptionController.find"}}, "/plans/{id}": {"put": {"x-controller-name": "PlanController", "x-operation-name": "replaceById", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Plan PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7010   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Plan"}}}, "x-parameter-index": 1}, "operationId": "PlanController.replaceById"}, "patch": {"x-controller-name": "PlanController", "x-operation-name": "updateById", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Plan PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7010   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanPartial"}}}, "x-parameter-index": 1}, "operationId": "PlanController.updateById"}, "get": {"x-controller-name": "PlanController", "x-operation-name": "findById", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7008   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plans.Filter"}}}}], "operationId": "PlanController.findById"}, "delete": {"x-controller-name": "PlanController", "x-operation-name": "deleteById", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Plan DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7011   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "PlanController.deleteById"}}, "/plans": {"post": {"x-controller-name": "PlanController", "x-operation-name": "create", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Plan"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7009   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPlan"}}}}, "operationId": "PlanController.create"}, "patch": {"x-controller-name": "PlanController", "x-operation-name": "updateAll", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7010   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "plans.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Plan>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlanPartial"}}}}, "operationId": "PlanController.updateAll"}, "get": {"x-controller-name": "PlanController", "x-operation-name": "find", "tags": ["PlanController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Plan model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PlanWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7008   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/plans.Filter1"}}}}], "operationId": "PlanController.find"}}, "/prices": {"post": {"x-controller-name": "PriceController", "x-operation-name": "create", "tags": ["PriceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "price model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7013   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewPrice"}}}}, "operationId": "PriceController.create"}}, "/resources/count": {"get": {"x-controller-name": "ResourceController", "x-operation-name": "count", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Resource model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7016   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "resources.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Resource>"}}}}], "operationId": "ResourceController.count"}}, "/resources/{id}": {"put": {"x-controller-name": "ResourceController", "x-operation-name": "replaceById", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Resource PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7014   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}, "x-parameter-index": 1}, "operationId": "ResourceController.replaceById"}, "patch": {"x-controller-name": "ResourceController", "x-operation-name": "updateById", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Resource PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7014   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourcePartial"}}}, "x-parameter-index": 1}, "operationId": "ResourceController.updateById"}, "get": {"x-controller-name": "ResourceController", "x-operation-name": "findById", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Resource model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourceWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7016   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/resources.Filter"}}}}], "operationId": "ResourceController.findById"}, "delete": {"x-controller-name": "ResourceController", "x-operation-name": "deleteById", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Resource DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7015   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "ResourceController.deleteById"}}, "/resources": {"post": {"x-controller-name": "ResourceController", "x-operation-name": "create", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Resource model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7013   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewResource"}}}}, "operationId": "ResourceController.create"}, "patch": {"x-controller-name": "ResourceController", "x-operation-name": "updateAll", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Resource PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7014   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "resources.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Resource>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResourcePartial"}}}}, "operationId": "ResourceController.updateAll"}, "get": {"x-controller-name": "ResourceController", "x-operation-name": "find", "tags": ["ResourceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Resource model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ResourceWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7016   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/resources.Filter1"}}}}], "operationId": "ResourceController.find"}}, "/services/count": {"get": {"x-controller-name": "ServiceController", "x-operation-name": "count", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Service model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7020   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "services.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Service>"}}}}], "operationId": "ServiceController.count"}}, "/services/{id}": {"put": {"x-controller-name": "ServiceController", "x-operation-name": "replaceById", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Service PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7018   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Service"}}}, "x-parameter-index": 1}, "operationId": "ServiceController.replaceById"}, "patch": {"x-controller-name": "ServiceController", "x-operation-name": "updateById", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Service PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7018   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicePartial"}}}, "x-parameter-index": 1}, "operationId": "ServiceController.updateById"}, "get": {"x-controller-name": "ServiceController", "x-operation-name": "findById", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Service model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7020   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/services.Filter"}}}}], "operationId": "ServiceController.findById"}, "delete": {"x-controller-name": "ServiceController", "x-operation-name": "deleteById", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Service DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7019   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "ServiceController.deleteById"}}, "/services": {"post": {"x-controller-name": "ServiceController", "x-operation-name": "create", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Service model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Service"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7017   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewService"}}}}, "operationId": "ServiceController.create"}, "patch": {"x-controller-name": "ServiceController", "x-operation-name": "updateAll", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Service PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7018   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "services.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Service>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServicePartial"}}}}, "operationId": "ServiceController.updateAll"}, "get": {"x-controller-name": "ServiceController", "x-operation-name": "find", "tags": ["ServiceController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Service model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7020   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/services.Filter1"}}}}], "operationId": "ServiceController.find"}}, "/software-level": {"get": {"x-controller-name": "SoftwareLevelController", "x-operation-name": "find", "tags": ["SoftwareLevelController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Plan has many SoftwareLevel", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SoftwareLevel"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 20207   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "SoftwareLevelController.find"}}, "/strategies/count": {"get": {"x-controller-name": "StrategyController", "x-operation-name": "count", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Strategy model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewStrategy   |\n| 5   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "strategies.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Strategy>"}}}}], "operationId": "StrategyController.count"}}, "/strategies/{id}": {"put": {"x-controller-name": "StrategyController", "x-operation-name": "replaceById", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Strategy PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateStrategy   |\n| 7   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Strategy"}}}, "x-parameter-index": 1}, "operationId": "StrategyController.replaceById"}, "patch": {"x-controller-name": "StrategyController", "x-operation-name": "updateById", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Strategy PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateStrategy   |\n| 7   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyPartial"}}}, "x-parameter-index": 1}, "operationId": "StrategyController.updateById"}, "get": {"x-controller-name": "StrategyController", "x-operation-name": "findById", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Strategy model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewStrategy   |\n| 5   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/strategies.Filter"}}}}], "operationId": "StrategyController.findById"}, "delete": {"x-controller-name": "StrategyController", "x-operation-name": "deleteById", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Strategy DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteStrategy   |\n| 8   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "StrategyController.deleteById"}}, "/strategies": {"post": {"x-controller-name": "StrategyController", "x-operation-name": "create", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Strategy model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Strategy"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateStrategy   |\n| 6   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewStrategy"}}}}, "operationId": "StrategyController.create"}, "patch": {"x-controller-name": "StrategyController", "x-operation-name": "updateAll", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Strategy PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateStrategy   |\n| 7   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "strategies.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Strategy>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyPartial"}}}}, "operationId": "StrategyController.updateAll"}, "get": {"x-controller-name": "StrategyController", "x-operation-name": "find", "tags": ["StrategyController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Strategy model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StrategyWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewStrategy   |\n| 5   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/strategies.Filter1"}}}}], "operationId": "StrategyController.find"}}, "/subscriptions/count": {"get": {"x-controller-name": "SubscriptionController", "x-operation-name": "count", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Subscription model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7004   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "subscriptions.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Subscription>"}}}}], "operationId": "SubscriptionController.count"}}, "/subscriptions/expire-soon": {"get": {"x-controller-name": "SubscriptionController", "x-operation-name": "expireSoonSubscription", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Subscription model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7004   |\n", "operationId": "SubscriptionController.expireSoonSubscription"}}, "/subscriptions/expired": {"get": {"x-controller-name": "SubscriptionController", "x-operation-name": "expiredSubscription", "tags": ["SubscriptionController"], "description": "API that will return newly expired subscriptions\n\n| Permissions |\n| ------- |\n| 7004   |\n", "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Subscription model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionWithRelations"}}}}}}, "parameters": [{"name": "days", "in": "header", "schema": {"type": "number"}}], "operationId": "SubscriptionController.expiredSubscription"}}, "/subscriptions/{id}/cancel": {"post": {"x-controller-name": "SubscriptionController", "x-operation-name": "cancelSubscription", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Subscription cancel POST success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7002   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "SubscriptionController.cancelSubscription"}}, "/subscriptions/{subscriberId}/renew-invite": {"post": {"x-controller-name": "SubscriptionController", "x-operation-name": "renewSubscription", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Renew Subscription invite POST success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7002   |\n", "parameters": [{"name": "subscriberId", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "statusId", "in": "query", "schema": {"type": "string"}}], "operationId": "SubscriptionController.renewSubscription"}}, "/subscriptions/{id}/resume": {"post": {"x-controller-name": "SubscriptionController", "x-operation-name": "resumeSubscription", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Subscription resume POST success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7002   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "SubscriptionController.resumeSubscription"}}, "/subscriptions/{id}/suspend": {"post": {"x-controller-name": "SubscriptionController", "x-operation-name": "suspendSubscription", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Subscription suspend POST success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7002   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "SubscriptionController.suspendSubscription"}}, "/subscriptions/{id}": {"put": {"x-controller-name": "SubscriptionController", "x-operation-name": "replaceById", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Subscription PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7002   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}, "x-parameter-index": 1}, "operationId": "SubscriptionController.replaceById"}, "patch": {"x-controller-name": "SubscriptionController", "x-operation-name": "updateById", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Subscription PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7002   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionDtoPartial"}}}, "x-parameter-index": 1}, "operationId": "SubscriptionController.updateById"}, "get": {"x-controller-name": "SubscriptionController", "x-operation-name": "findById", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Subscription model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionWithRelations"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7004   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/subscriptions.Filter"}}}}], "operationId": "SubscriptionController.findById"}, "delete": {"x-controller-name": "SubscriptionController", "x-operation-name": "deleteById", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Subscription DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| 7003   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "SubscriptionController.deleteById"}}, "/subscriptions": {"post": {"x-controller-name": "SubscriptionController", "x-operation-name": "create", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Subscription model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7001   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewSubscription"}}}}, "operationId": "SubscriptionController.create"}, "patch": {"x-controller-name": "SubscriptionController", "x-operation-name": "updateAll", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Subscription PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7002   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "subscriptions.Where<PERSON><PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Subscription>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPartial"}}}}, "operationId": "SubscriptionController.updateAll"}, "get": {"x-controller-name": "SubscriptionController", "x-operation-name": "find", "tags": ["SubscriptionController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Subscription model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 7004   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/subscriptions.Filter1"}}}}], "operationId": "SubscriptionController.find"}}, "/tenant-billings/all-status/metrics": {"get": {"x-controller-name": "TenantBillingsController", "x-operation-name": "findAllStatusMetrics", "tags": ["TenantBillingsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object of all possible tenant billing status", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "TenantBillingsController.findAllStatusMetrics"}}, "/tenant-billings/all-status": {"get": {"x-controller-name": "TenantBillingsController", "x-operation-name": "findAllStatus", "tags": ["TenantBillingsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Object of all possible tenant billing status", "permissions": ["10216"], "content": {"application/json": {"schema": {"type": "object", "items": {}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 10216   |\n", "operationId": "TenantBillingsController.findAllStatus"}}, "/tenant-billings/count": {"get": {"x-controller-name": "TenantBillingsController", "x-operation-name": "count", "tags": ["TenantBillingsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Plan model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "tenant_billings_view.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<TenantBillingsView>"}}}}], "operationId": "TenantBillingsController.count"}}, "/tenant-billings": {"get": {"x-controller-name": "TenantBillingsController", "x-operation-name": "find", "tags": ["TenantBillingsController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of ConfigureDevice model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TenantBillingsViewWithRelations"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| 6027   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/tenant_billings_view.Filter"}}}}], "operationId": "TenantBillingsController.find"}}, "/webhooks/payment": {"post": {"x-controller-name": "WebhookController", "x-operation-name": "handleWebhook", "tags": ["WebhookController"], "description": "", "responses": {"200": {"description": "Return value of WebhookController.handleWebhook", "content": {}}}, "requestBody": {"content": {"application/json": {"x-parser": "raw", "schema": {"type": "object"}}}, "description": "Payment Provider webhook payload", "required": true}, "operationId": "WebhookController.handleWebhook"}}, "/": {"get": {"x-controller-name": "HomePageController", "x-operation-name": "homePage", "tags": ["HomePageController"], "responses": {"200": {"description": "Home Page", "content": {"text/html": {"schema": {"type": "string"}}}}}, "description": "", "operationId": "HomePageController.homePage"}}}, "components": {"securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Feature": {"title": "Feature", "type": "object", "description": "The features table", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "description": {"type": "string"}, "defaultValue": {"type": "string"}, "type": {"type": "string"}, "metadata": {"type": "object"}}, "required": ["name", "key"], "additionalProperties": false}, "NewFeature": {"title": "NewFeature", "type": "object", "description": "The features table (tsType: Feature, schemaOptions: { title: 'NewFeature' })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "description": {"type": "string"}, "defaultValue": {"type": "string"}, "type": {"type": "string"}, "metadata": {"type": "object"}}, "required": ["name", "key"], "additionalProperties": false}, "FeatureWithRelations": {"title": "FeatureWithRelations", "type": "object", "description": "The features table (tsType: FeatureWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "description": {"type": "string"}, "defaultValue": {"type": "string"}, "type": {"type": "string"}, "metadata": {"type": "object"}}, "required": ["name", "key"], "additionalProperties": false}, "FeaturePartial": {"title": "FeaturePartial", "type": "object", "description": "The features table (tsType: Partial<Feature>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "description": {"type": "string"}, "defaultValue": {"type": "string"}, "type": {"type": "string"}, "metadata": {"type": "object"}}, "additionalProperties": false}, "FeatureValues": {"title": "FeatureValues", "type": "object", "description": "The feature-values table", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "featureKey": {"type": "string"}, "strategyKey": {"type": "string"}, "strategyEntityId": {"type": "string"}, "status": {"type": "boolean"}, "value": {"type": "string"}}, "additionalProperties": false}, "NewFeatureValues": {"title": "NewFeatureValues", "type": "object", "description": "The feature-values table (tsType: FeatureValues, schemaOptions: { title: 'NewFeatureValues' })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "featureKey": {"type": "string"}, "strategyKey": {"type": "string"}, "strategyEntityId": {"type": "string"}, "status": {"type": "boolean"}, "value": {"type": "string"}}, "additionalProperties": false}, "FeatureValuesWithRelations": {"title": "FeatureValuesWithRelations", "type": "object", "description": "The feature-values table (tsType: FeatureValuesWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "featureKey": {"type": "string"}, "strategyKey": {"type": "string"}, "strategyEntityId": {"type": "string"}, "status": {"type": "boolean"}, "value": {"type": "string"}}, "additionalProperties": false}, "FeatureValuesPartial": {"title": "FeatureValuesPartial", "type": "object", "description": "The feature-values table (tsType: Partial<FeatureValues>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "featureKey": {"type": "string"}, "strategyKey": {"type": "string"}, "strategyEntityId": {"type": "string"}, "status": {"type": "boolean"}, "value": {"type": "string"}}, "additionalProperties": false}, "Strategy": {"title": "Strategy", "type": "object", "description": "The strategies table", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "priority": {"type": "number"}}, "required": ["name", "key"], "additionalProperties": false}, "NewStrategy": {"title": "NewStrategy", "type": "object", "description": "The strategies table (tsType: Strategy, schemaOptions: { title: 'NewStrategy' })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "priority": {"type": "number"}}, "required": ["name", "key"], "additionalProperties": false}, "StrategyWithRelations": {"title": "StrategyWithRelations", "type": "object", "description": "The strategies table (tsType: StrategyWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "priority": {"type": "number"}}, "required": ["name", "key"], "additionalProperties": false}, "StrategyPartial": {"title": "StrategyPartial", "type": "object", "description": "The strategies table (tsType: Partial<Strategy>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "key": {"type": "string"}, "priority": {"type": "number"}}, "additionalProperties": false}, "BillingCycle": {"title": "BillingCycle", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "cycleName": {"type": "string"}, "duration": {"type": "number"}, "durationUnit": {"type": "string"}, "description": {"type": "string"}}, "required": ["cycleName", "duration", "durationUnit"], "additionalProperties": false}, "NewBillingCycle": {"title": "NewBillingCycle", "type": "object", "description": "(tsType: Omit<BillingCycle, 'id'>, schemaOptions: { title: 'NewBillingCycle', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "cycleName": {"type": "string"}, "duration": {"type": "number"}, "durationUnit": {"type": "string"}, "description": {"type": "string"}}, "required": ["cycleName", "duration", "durationUnit"], "additionalProperties": false, "x-typescript-type": "Omit<BillingCycle, 'id'>"}, "BillingCycleWithRelations": {"title": "BillingCycleWithRelations", "type": "object", "description": "(tsType: BillingCycleWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "cycleName": {"type": "string"}, "duration": {"type": "number"}, "durationUnit": {"type": "string"}, "description": {"type": "string"}}, "required": ["cycleName", "duration", "durationUnit"], "additionalProperties": false, "x-typescript-type": "BillingCycleWithRelations"}, "BillingCyclePartial": {"title": "BillingCyclePartial", "type": "object", "description": "(tsType: Partial<BillingCycle>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "cycleName": {"type": "string"}, "duration": {"type": "number"}, "durationUnit": {"type": "string"}, "description": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<BillingCycle>"}, "Currency": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"id": {"type": "string"}, "currencyCode": {"type": "string"}, "currencyName": {"type": "string"}, "symbol": {"type": "string"}, "country": {"type": "string"}}, "required": ["currencyCode", "currencyName"], "additionalProperties": false}, "NewCurrency": {"title": "NewCurrency", "type": "object", "description": "(tsType: Omit<Currency, 'id'>, schemaOptions: { title: 'NewCurrency', exclude: [ 'id' ] })", "properties": {"currencyCode": {"type": "string"}, "currencyName": {"type": "string"}, "symbol": {"type": "string"}, "country": {"type": "string"}}, "required": ["currencyCode", "currencyName"], "additionalProperties": false, "x-typescript-type": "Omit<Currency, 'id'>"}, "CurrencyWithRelations": {"title": "CurrencyWithRelations", "type": "object", "description": "(tsType: C<PERSON>rencyWithRelations, schemaOptions: { includeRelations: true })", "properties": {"id": {"type": "string"}, "currencyCode": {"type": "string"}, "currencyName": {"type": "string"}, "symbol": {"type": "string"}, "country": {"type": "string"}}, "required": ["currencyCode", "currencyName"], "additionalProperties": false, "x-typescript-type": "CurrencyWithRelations"}, "CurrencyPartial": {"title": "CurrencyPartial", "type": "object", "description": "(tsType: Partial<Currency>, schemaOptions: { partial: true })", "properties": {"id": {"type": "string"}, "currencyCode": {"type": "string"}, "currencyName": {"type": "string"}, "symbol": {"type": "string"}, "country": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Currency>"}, "Plan": {"title": "Plan", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the plan"}, "description": {"type": "string", "description": "description of the plan"}, "tier": {"type": "string", "description": "Tier of the plan.", "enum": ["premium", "standard"]}, "price": {"type": "number"}, "metaData": {"type": "object", "description": "Meta data of the plan"}, "status": {"type": "number", "description": "Status of the plan.", "enum": [0, 1, 2]}, "version": {"type": "string", "description": "current version of the plan."}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "productRefId": {"type": "string", "description": "product reference ID of the plan."}, "billingCycleId": {"type": "string"}, "currencyId": {"type": "string"}, "configureDeviceId": {"type": "string"}, "planSizeId": {"type": "string"}, "softwareLevelId": {"type": "string"}}, "required": ["name", "tier", "price", "status", "version", "productRefId"], "additionalProperties": false}, "NewPlan": {"title": "NewPlan", "type": "object", "description": "(tsType: Omit<Plan, 'id' | 'status' | 'productRefId'>, schemaOptions: { title: 'NewPlan', exclude: [ 'id', 'status', 'productRefId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string", "description": "name of the plan"}, "description": {"type": "string", "description": "description of the plan"}, "tier": {"type": "string", "description": "Tier of the plan.", "enum": ["premium", "standard"]}, "price": {"type": "number"}, "metaData": {"type": "object", "description": "Meta data of the plan"}, "version": {"type": "string", "description": "current version of the plan."}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "billingCycleId": {"type": "string"}, "currencyId": {"type": "string"}, "configureDeviceId": {"type": "string"}, "planSizeId": {"type": "string"}, "softwareLevelId": {"type": "string"}}, "required": ["name", "tier", "price", "version"], "additionalProperties": false, "x-typescript-type": "Omit<Plan, 'id' | 'status' | 'productRefId'>"}, "PlanWithRelations": {"title": "PlanWithRelations", "type": "object", "description": "(tsType: PlanWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the plan"}, "description": {"type": "string", "description": "description of the plan"}, "tier": {"type": "string", "description": "Tier of the plan."}, "size": {"type": "string", "description": "Size of the plan."}, "price": {"type": "number"}, "metaData": {"type": "object", "description": "Meta data of the plan"}, "billingCycleId": {"type": "string"}, "currencyId": {"type": "string"}, "billingCycle": {"$ref": "#/components/schemas/BillingCycleWithRelations"}, "foreignKey": {}, "currency": {"$ref": "#/components/schemas/CurrencyWithRelations"}}, "required": ["name", "tier", "price"], "additionalProperties": false, "x-typescript-type": "PlanWithRelations"}, "PlanPartial": {"title": "PlanPartial", "type": "object", "description": "(tsType: Partial<Plan>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the plan"}, "description": {"type": "string", "description": "description of the plan"}, "tier": {"type": "string", "description": "Tier of the plan.", "enum": ["premium", "standard"]}, "price": {"type": "number"}, "metaData": {"type": "object", "description": "Meta data of the plan"}, "status": {"type": "number", "description": "Status of the plan.", "enum": [0, 1, 2]}, "version": {"type": "string", "description": "current version of the plan."}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "productRefId": {"type": "string", "description": "product reference ID of the plan."}, "billingCycleId": {"type": "string"}, "currencyId": {"type": "string"}, "configureDeviceId": {"type": "string"}, "planSizeId": {"type": "string"}, "softwareLevelId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Plan>"}, "Resource": {"title": "Resource", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the resource"}, "config": {"type": "object", "description": "config of the resource"}}, "required": ["name", "config"], "additionalProperties": false}, "NewResource": {"title": "NewResource", "type": "object", "description": "(tsType: Omit<Resource, 'id'>, schemaOptions: { title: 'NewResource', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string", "description": "name of the resource"}, "config": {"type": "object", "description": "config of the resource"}}, "required": ["name", "config"], "additionalProperties": false, "x-typescript-type": "Omit<Resource, 'id'>"}, "ResourceWithRelations": {"title": "ResourceWithRelations", "type": "object", "description": "(tsType: ResourceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the resource"}, "config": {"type": "object", "description": "config of the resource"}}, "required": ["name", "config"], "additionalProperties": false, "x-typescript-type": "ResourceWithRelations"}, "ResourcePartial": {"title": "ResourcePartial", "type": "object", "description": "(tsType: Partial<Resource>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the resource"}, "config": {"type": "object", "description": "config of the resource"}}, "additionalProperties": false, "x-typescript-type": "Partial<Resource>"}, "Service": {"title": "Service", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the service"}}, "required": ["name"], "additionalProperties": false}, "NewService": {"title": "NewService", "type": "object", "description": "(tsType: Omit<Service, 'id'>, schemaOptions: { title: 'NewService', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string", "description": "name of the service"}}, "required": ["name"], "additionalProperties": false, "x-typescript-type": "Omit<Service, 'id'>"}, "ServiceWithRelations": {"title": "ServiceWithRelations", "type": "object", "description": "(tsType: ServiceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the service"}}, "required": ["name"], "additionalProperties": false, "x-typescript-type": "ServiceWithRelations"}, "ServicePartial": {"title": "ServicePartial", "type": "object", "description": "(tsType: Partial<Service>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string", "description": "name of the service"}}, "additionalProperties": false, "x-typescript-type": "Partial<Service>"}, "Subscription": {"title": "Subscription", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "subscriberId": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "externalSubscriptionId": {"type": "string"}, "priceRefId": {"type": "string", "description": "price reference ID of the subscription."}, "status": {"type": "number", "description": "status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}, "trialEndDate": {"type": "string"}, "planId": {"type": "string", "description": "plan id of the subscription"}}, "required": ["subscriberId", "totalCost", "status"], "additionalProperties": false}, "NewSubscription": {"title": "NewSubscription", "type": "object", "description": "(tsType: Omit<Subscription, 'id' | 'startDate' | 'endDate' | 'externalSubscriptionId'>, schemaOptions: { title: 'NewSubscription', exclude: [ 'id', 'startDate', 'endDate', 'externalSubscriptionId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "subscriberId": {"type": "string"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "priceRefId": {"type": "string", "description": "price reference ID of the subscription."}, "status": {"type": "number", "description": "status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}, "trialEndDate": {"type": "string"}, "planId": {"type": "string", "description": "plan id of the subscription"}}, "required": ["subscriberId", "totalCost", "status"], "additionalProperties": false, "x-typescript-type": "Omit<Subscription, 'id' | 'startDate' | 'endDate' | 'externalSubscriptionId'>"}, "ConfigureDeviceWithRelations": {"title": "ConfigureDeviceWithRelations", "type": "object", "description": "(tsType: ConfigureDeviceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "min": {"type": "number"}, "max": {"type": "number"}, "replica": {"type": "number"}, "computeSize": {"type": "string"}, "dbSize": {"type": "string"}}, "required": ["min", "max"], "additionalProperties": false, "x-typescript-type": "ConfigureDeviceWithRelations"}, "PlanSizesWithRelations": {"title": "PlanSizesWithRelations", "type": "object", "description": "(tsType: PlanSizesWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "size": {"type": "string"}, "config": {"type": "object"}}, "required": ["size"], "additionalProperties": false, "x-typescript-type": "PlanSizesWithRelations"}, "PlanHistoryWithRelations": {"title": "PlanHistoryWithRelations", "type": "object", "description": "(tsType: PlanHistoryWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "price": {"type": "number"}, "version": {"type": "string"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "planId": {"type": "string"}, "plan": {"$ref": "#/components/schemas/PlanWithRelations"}, "foreignKey": {}}, "required": ["version"], "additionalProperties": false, "x-typescript-type": "PlanHistoryWithRelations"}, "SoftwareLevelWithRelations": {"title": "SoftwareLevelWithRelations", "type": "object", "description": "(tsType: SoftwareLevelWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "level": {"type": "string", "maxLength": 100}}, "required": ["level"], "additionalProperties": false, "x-typescript-type": "SoftwareLevelWithRelations"}, "SubscriptionWithRelations": {"title": "SubscriptionWithRelations", "type": "object", "description": "(tsType: SubscriptionWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "subscriberId": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "status": {"type": "number", "description": "status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)", "enum": [0, 1, 2, 3, 4]}, "planId": {"type": "string", "description": "plan id of the subscription"}, "invoiceId": {"type": "string", "description": "invoice id of the subscription"}, "plan": {"$ref": "#/components/schemas/PlanWithRelations"}, "foreignKey": {}, "invoice": {"$ref": "#/components/schemas/InvoiceWithRelations"}}, "required": ["subscriberId", "startDate", "endDate", "status"], "additionalProperties": false, "x-typescript-type": "SubscriptionWithRelations"}, "SubscriptionPartial": {"title": "SubscriptionPartial", "type": "object", "description": "(tsType: Partial<Subscription>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "subscriberId": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "externalSubscriptionId": {"type": "string"}, "priceRefId": {"type": "string", "description": "price reference ID of the subscription."}, "status": {"type": "number", "description": "status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}, "trialEndDate": {"type": "string"}, "planId": {"type": "string", "description": "plan id of the subscription"}}, "additionalProperties": false, "x-typescript-type": "Partial<Subscription>"}, "SubscriptionDtoPartial": {"title": "SubscriptionDtoPartial", "type": "object", "description": "(tsType: Partial<SubscriptionDto>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "subscriberId": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "externalSubscriptionId": {"type": "string"}, "priceRefId": {"type": "string", "description": "price reference ID of the subscription."}, "status": {"type": "number", "description": "status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}, "trialEndDate": {"type": "string"}, "planId": {"type": "string", "description": "plan id of the subscription"}, "prorationBehavior": {"type": "string", "enum": ["create_prorations", "none", "always_invoice"]}}, "additionalProperties": false, "x-typescript-type": "Partial<SubscriptionDto>"}, "SubscriptionDto": {"title": "SubscriptionDto", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "subscriberId": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "totalCost": {"type": "number", "description": "total cost of the tenant"}, "numberOfUsers": {"type": "number", "description": "number of users for the tenant"}, "externalSubscriptionId": {"type": "string"}, "priceRefId": {"type": "string", "description": "price reference ID of the subscription."}, "status": {"type": "number", "description": "status of the subscription, it can be - 0(pending), 1(active), 2(inactive), 3(cancelled) and 4(expired)", "enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]}, "trialEndDate": {"type": "string"}, "planId": {"type": "string", "description": "plan id of the subscription"}, "prorationBehavior": {"type": "string", "enum": ["create_prorations", "none", "always_invoice"]}}, "required": ["subscriberId", "totalCost", "status"], "additionalProperties": false}, "InvoiceWithRelations": {"title": "InvoiceWithRelations", "type": "object", "description": "(tsType: InvoiceWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "invoiceId": {"type": "string"}, "invoiceStatus": {"type": "string", "description": "payment or invoice status"}, "billingCustomerId": {"type": "string"}, "amount": {"type": "number"}, "tax": {"type": "number"}, "discount": {"type": "number"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "startDate": {"type": "string", "description": "start date for the period this invoice is generated for"}, "endDate": {"type": "string", "description": "end date for the period this invoice is generated for"}, "invoiceNumber": {"type": "string", "description": "unique identifier for the invoice"}, "billingCustomer": {"$ref": "#/components/schemas/BillingCustomerWithRelations"}, "foreignKey": {}}, "required": ["invoiceId", "amount", "tax", "dueDate", "startDate", "endDate"], "additionalProperties": false, "x-typescript-type": "InvoiceWithRelations"}, "PlanSizes": {"title": "PlanSizes", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "size": {"type": "string"}, "config": {"type": "object"}}, "required": ["size"], "additionalProperties": false}, "NewPlanSizes": {"title": "NewPlanSizes", "type": "object", "description": "(tsType: Omit<PlanSizes, 'id'>, schemaOptions: { title: 'NewPlanSizes', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "size": {"type": "string"}, "config": {"type": "object"}}, "required": ["size"], "additionalProperties": false, "x-typescript-type": "Omit<PlanSizes, 'id'>"}, "PlanSizesPartial": {"title": "PlanSizesPartial", "type": "object", "description": "(tsType: Partial<PlanSizes>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "size": {"type": "string"}, "config": {"type": "object"}}, "additionalProperties": false, "x-typescript-type": "Partial<PlanSizes>"}, "AddressDto": {"title": "AddressDto", "type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "company": {"type": "string"}, "phone": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}, "country": {"type": "string"}, "options": {"type": "object"}}, "required": ["email", "city", "state", "zip", "country"], "additionalProperties": false}, "NewBillingCustomer": {"title": "NewBillingCustomer", "type": "object", "description": "(tsType: CustomerDto, schemaOptions: { title: 'NewBillingCustomer' })", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "company": {"type": "string"}, "phone": {"type": "string"}, "billingAddress": {"$ref": "#/components/schemas/AddressDto"}, "options": {"type": "object"}, "name": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "CustomerDto"}, "NewCustomer": {"title": "NewCustomer", "type": "object", "description": "(tsType: Omit<CustomerDto, 'id'>, schemaOptions: { title: 'NewCustomer', exclude: [ 'id' ] })", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "company": {"type": "string"}, "phone": {"type": "string"}, "billingAddress": {"$ref": "#/components/schemas/AddressDto"}, "options": {"type": "object"}, "name": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Omit<CustomerDto, 'id'>"}, "CustomerDto": {"title": "CustomerDto", "type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "company": {"type": "string"}, "phone": {"type": "string"}, "billingAddress": {"$ref": "#/components/schemas/AddressDto"}, "options": {"type": "object"}, "name": {"type": "string"}}, "additionalProperties": false}, "CustomerDtoPartial": {"title": "CustomerDtoPartial", "type": "object", "description": "(tsType: Partial<CustomerDto>, schemaOptions: { partial: true })", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string"}, "company": {"type": "string"}, "phone": {"type": "string"}, "billingAddress": {"$ref": "#/components/schemas/AddressDto"}, "options": {"type": "object"}, "name": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<CustomerDto>"}, "ChargeDto": {"title": "ChargeDto", "type": "object", "properties": {"amount": {"type": "number"}, "description": {"type": "string"}}, "required": ["amount", "description"], "additionalProperties": false}, "InvoiceDto": {"title": "InvoiceDto", "type": "object", "properties": {"id": {"type": "string"}, "customerId": {"type": "string"}, "currencyCode": {"type": "string"}, "options": {"type": "object"}, "shippingAddress": {"$ref": "#/components/schemas/AddressDto"}, "charges": {"type": "array", "items": {"$ref": "#/components/schemas/ChargeDto"}}, "status": {"type": "string"}}, "additionalProperties": false}, "newInvoice": {"title": "newInvoice", "type": "object", "description": "(tsType: Omit<InvoiceDto, 'id' | 'status'>, schemaOptions: { title: 'newInvoice', exclude: [ 'id', 'status' ] })", "properties": {"customerId": {"type": "string"}, "currencyCode": {"type": "string"}, "options": {"type": "object"}, "shippingAddress": {"$ref": "#/components/schemas/AddressDto"}, "charges": {"type": "array", "items": {"$ref": "#/components/schemas/ChargeDto"}}}, "additionalProperties": false, "x-typescript-type": "Omit<InvoiceDto, 'id' | 'status'>"}, "PaymentSourceDto": {"title": "PaymentSourceDto", "type": "object", "properties": {"id": {"type": "string"}, "customerId": {"type": "string"}, "card": {"type": "object", "properties": {"gatewayAccountId": {"type": "string"}, "number": {"type": "string"}, "expiryMonth": {"type": "number"}, "expiryear": {"type": "number"}, "cvv": {"type": "string"}}, "required": ["gatewayAccountId", "number", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "cvv"]}, "options": {"type": "object"}}, "additionalProperties": false}, "NewPaymentSource": {"title": "NewPaymentSource", "type": "object", "description": "(tsType: Omit<PaymentSourceDto, 'id'>, schemaOptions: { title: 'NewPaymentSource', exclude: [ 'id' ] })", "properties": {"customerId": {"type": "string"}, "card": {"type": "object", "properties": {"gatewayAccountId": {"type": "string"}, "number": {"type": "string"}, "expiryMonth": {"type": "number"}, "expiryear": {"type": "number"}, "cvv": {"type": "string"}}, "required": ["gatewayAccountId", "number", "expiry<PERSON><PERSON><PERSON>", "expiryYear", "cvv"]}, "options": {"type": "object"}}, "additionalProperties": false, "x-typescript-type": "Omit<PaymentSourceDto, 'id'>"}, "BillingCustomerWithRelations": {"title": "BillingCustomerWithRelations", "type": "object", "description": "contacts belonging to a tenant (tsType: BillingCustomerWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "tenantId": {"type": "string"}, "customerId": {"type": "string"}, "paymentSourceId": {"type": "string"}, "invoices": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceWithRelations"}}}, "required": ["tenantId", "customerId"], "additionalProperties": false}, "TenantBillingsViewWithRelations": {"title": "TenantBillingsViewWithRelations", "type": "object", "description": "(tsType: TenantBillingsViewWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "invoiceId": {"type": "string"}, "invoiceStatus": {"type": "string", "description": "payment or invoice status"}, "billingCustomerId": {"type": "string"}, "amount": {"type": "number"}, "tax": {"type": "number"}, "discount": {"type": "number"}, "dueDate": {"type": "string", "description": "due date for the invoice"}, "customerId": {"type": "string"}, "tenantId": {"type": "string"}, "tenantName": {"type": "string"}, "invoiceNumber": {"type": "string"}, "billingCustomer": {"$ref": "#/components/schemas/BillingCustomerWithRelations"}, "foreignKey": {}}, "required": ["invoiceId", "amount", "tax", "dueDate", "customerId", "tenantId", "tenantName"], "additionalProperties": false, "x-typescript-type": "TenantBillingsViewWithRelations"}, "SoftwareLevel": {"title": "SoftwareLevel", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "level": {"type": "string", "maxLength": 100}}, "required": ["level"], "additionalProperties": false}, "PriceDto": {"title": "PriceDto", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the object."}, "active": {"type": "boolean", "description": "Whether the price can be used for new purchases."}, "currency": {"type": "string", "description": "Three-letter ISO currency code, in lowercase. Must be a supported currency.", "enum": ["usd", "eur", "inr", "gbp"]}, "metadata": {"type": "object", "description": "Set of key-value pairs that you can attach to an object for storing additional structured information."}, "product": {"type": "string", "description": "The ID of the product this price is associated with."}, "recurring": {"type": "object", "description": "The recurring components of a price such as interval and usage_type."}, "unit_amount": {"type": "number", "description": "The unit amount in cents to be charged. Nullable depending on billing scheme."}}, "required": ["currency", "product"], "additionalProperties": false}, "NewPrice": {"title": "NewPrice", "type": "object", "description": "(tsType: Omit<PriceDto, 'id'>, schemaOptions: { title: 'NewPrice', exclude: [ 'id' ] })", "properties": {"active": {"type": "boolean", "description": "Whether the price can be used for new purchases."}, "currency": {"type": "string", "description": "Three-letter ISO currency code, in lowercase. Must be a supported currency.", "enum": ["usd", "eur", "inr", "gbp"]}, "metadata": {"type": "object", "description": "Set of key-value pairs that you can attach to an object for storing additional structured information."}, "product": {"type": "string", "description": "The ID of the product this price is associated with."}, "recurring": {"type": "object", "description": "The recurring components of a price such as interval and usage_type."}, "unit_amount": {"type": "number", "description": "The unit amount in cents to be charged. Nullable depending on billing scheme."}}, "required": ["currency", "product"], "additionalProperties": false, "x-typescript-type": "Omit<PriceDto, 'id'>"}, "PlanHistory": {"title": "PlanHistory", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "price": {"type": "number"}, "version": {"type": "string"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "planId": {"type": "string"}}, "required": ["version"], "additionalProperties": false}, "NewPlanHistoryInPlan": {"title": "NewPlanHistoryInPlan", "type": "object", "description": "(tsType: @loopback/repository-json-schema#Optional<Omit<PlanHistory, 'id'>, 'planId'>, schemaOptions: { title: 'NewPlanHistoryInPlan', exclude: [ 'id' ], optional: [ 'planId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "price": {"type": "number"}, "version": {"type": "string"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "planId": {"type": "string"}}, "required": ["version"], "additionalProperties": false, "x-typescript-type": "@loopback/repository-json-schema#Optional<Omit<PlanHistory, 'id'>, 'planId'>"}, "PlanHistoryPartial": {"title": "PlanHistoryPartial", "type": "object", "description": "(tsType: Partial<PlanHistory>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "price": {"type": "number"}, "version": {"type": "string"}, "allowedUnlimitedUsers": {"type": "boolean", "description": "Indicates if the plan allows unlimited users."}, "costPerUser": {"type": "number", "description": "price added per user for the plan."}, "planId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<PlanHistory>"}, "ConfigureDevice": {"title": "ConfigureDevice", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "min": {"type": "number"}, "max": {"type": "number"}, "replica": {"type": "number"}, "computeSize": {"type": "string"}, "dbSize": {"type": "string"}}, "required": ["min", "max"], "additionalProperties": false}, "NewConfigureDevice": {"title": "NewConfigureDevice", "type": "object", "description": "(tsType: Omit<ConfigureDevice, 'id'>, schemaOptions: { title: 'NewConfigureDevice', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "min": {"type": "number"}, "max": {"type": "number"}, "replica": {"type": "number"}, "computeSize": {"type": "string"}, "dbSize": {"type": "string"}}, "required": ["min", "max"], "additionalProperties": false, "x-typescript-type": "Omit<ConfigureDevice, 'id'>"}, "ConfigureDevicePartial": {"title": "ConfigureDevicePartial", "type": "object", "description": "(tsType: Partial<ConfigureDevice>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "min": {"type": "number"}, "max": {"type": "number"}, "replica": {"type": "number"}, "computeSize": {"type": "string"}, "dbSize": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<ConfigureDevice>"}, "billing_customer.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "billing_customer.ScopeFilter"}, "billing_customer.IncludeFilter.Items": {"title": "billing_customer.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["invoices"]}, "scope": {"$ref": "#/components/schemas/billing_customer.ScopeFilter"}}}, "billing_customer.Filter": {"type": "object", "title": "billing_customer.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "billing_customer.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "customerId": {"type": "boolean"}, "paymentSourceId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "tenantId", "customerId", "paymentSourceId"], "example": "deleted"}, "uniqueItems": true}], "title": "billing_customer.Fields"}, "include": {"title": "billing_customer.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/billing_customer.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<BillingCustomer>"}, "loopback.Count": {"type": "object", "title": "loopback.Count", "x-typescript-type": "@loopback/repository#Count", "properties": {"count": {"type": "number"}}}, "billing_cycles.Filter": {"type": "object", "title": "billing_cycles.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "cycleName": {"type": "boolean"}, "duration": {"type": "boolean"}, "durationUnit": {"type": "boolean"}, "description": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "cycleName", "duration", "durationUnit", "description"], "example": "deleted"}, "uniqueItems": true}], "title": "billing_cycles.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<BillingCycle>"}, "billing_cycles.Filter1": {"type": "object", "title": "billing_cycles.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "billing_cycles.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "cycleName": {"type": "boolean"}, "duration": {"type": "boolean"}, "durationUnit": {"type": "boolean"}, "description": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "cycleName", "duration", "durationUnit", "description"], "example": "deleted"}, "uniqueItems": true}], "title": "billing_cycles.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<BillingCycle>"}, "configure_devices.Filter": {"type": "object", "title": "configure_devices.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "configure_devices.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "min": {"type": "boolean"}, "max": {"type": "boolean"}, "replica": {"type": "boolean"}, "computeSize": {"type": "boolean"}, "dbSize": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "min", "max", "replica", "computeSize", "dbSize"], "example": "deleted"}, "uniqueItems": true}], "title": "configure_devices.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<ConfigureDevice>"}, "currencies.Filter": {"type": "object", "title": "currencies.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "currencyCode": {"type": "boolean"}, "currencyName": {"type": "boolean"}, "symbol": {"type": "boolean"}, "country": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "currencyCode", "currencyName", "symbol", "country"], "example": "id"}, "uniqueItems": true}], "title": "currencies.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Currency>"}, "currencies.Filter1": {"type": "object", "title": "currencies.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "currencies.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "currencyCode": {"type": "boolean"}, "currencyName": {"type": "boolean"}, "symbol": {"type": "boolean"}, "country": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "currencyCode", "currencyName", "symbol", "country"], "example": "id"}, "uniqueItems": true}], "title": "currencies.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Currency>"}, "feature_values.Filter": {"type": "object", "title": "feature_values.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "featureKey": {"type": "boolean"}, "strategyKey": {"type": "boolean"}, "strategyEntityId": {"type": "boolean"}, "status": {"type": "boolean"}, "value": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "<PERSON><PERSON><PERSON>", "strategy<PERSON>ey", "strategyEntityId", "status", "value"], "example": "deleted"}, "uniqueItems": true}], "title": "feature_values.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<FeatureValues>"}, "feature_values.Filter1": {"type": "object", "title": "feature_values.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "feature_values.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "featureKey": {"type": "boolean"}, "strategyKey": {"type": "boolean"}, "strategyEntityId": {"type": "boolean"}, "status": {"type": "boolean"}, "value": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "<PERSON><PERSON><PERSON>", "strategy<PERSON>ey", "strategyEntityId", "status", "value"], "example": "deleted"}, "uniqueItems": true}], "title": "feature_values.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<FeatureValues>"}, "features.Filter": {"type": "object", "title": "features.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "key": {"type": "boolean"}, "description": {"type": "boolean"}, "defaultValue": {"type": "boolean"}, "type": {"type": "boolean"}, "metadata": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "key", "description", "defaultValue", "type", "metadata"], "example": "deleted"}, "uniqueItems": true}], "title": "features.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Feature>"}, "features.Filter1": {"type": "object", "title": "features.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "features.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "key": {"type": "boolean"}, "description": {"type": "boolean"}, "defaultValue": {"type": "boolean"}, "type": {"type": "boolean"}, "metadata": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "key", "description", "defaultValue", "type", "metadata"], "example": "deleted"}, "uniqueItems": true}], "title": "features.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Feature>"}, "invoice.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "invoice.ScopeFilter"}, "invoice.IncludeFilter.Items": {"title": "invoice.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["billingCustomer"]}, "scope": {"$ref": "#/components/schemas/invoice.ScopeFilter"}}}, "invoice.Filter": {"type": "object", "title": "invoice.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "invoiceId": {"type": "boolean"}, "invoiceStatus": {"type": "boolean"}, "billingCustomerId": {"type": "boolean"}, "amount": {"type": "boolean"}, "tax": {"type": "boolean"}, "discount": {"type": "boolean"}, "dueDate": {"type": "boolean"}, "startDate": {"type": "boolean"}, "endDate": {"type": "boolean"}, "invoiceNumber": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "invoiceId", "invoiceStatus", "billingCustomerId", "amount", "tax", "discount", "dueDate", "startDate", "endDate", "invoiceNumber"], "example": "deleted"}, "uniqueItems": true}], "title": "invoice.Fields"}, "include": {"title": "invoice.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/invoice.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Invoice>"}, "invoice.Filter1": {"type": "object", "title": "invoice.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "invoice.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "invoiceId": {"type": "boolean"}, "invoiceStatus": {"type": "boolean"}, "billingCustomerId": {"type": "boolean"}, "amount": {"type": "boolean"}, "tax": {"type": "boolean"}, "discount": {"type": "boolean"}, "dueDate": {"type": "boolean"}, "startDate": {"type": "boolean"}, "endDate": {"type": "boolean"}, "invoiceNumber": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "invoiceId", "invoiceStatus", "billingCustomerId", "amount", "tax", "discount", "dueDate", "startDate", "endDate", "invoiceNumber"], "example": "deleted"}, "uniqueItems": true}], "title": "invoice.Fields"}, "include": {"title": "invoice.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/invoice.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Invoice>"}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "plan_sizes.Filter": {"type": "object", "title": "plan_sizes.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "size": {"type": "boolean"}, "config": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "size", "config"], "example": "deleted"}, "uniqueItems": true}], "title": "plan_sizes.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<PlanSizes>"}, "plan_sizes.Filter1": {"type": "object", "title": "plan_sizes.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "plan_sizes.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "size": {"type": "boolean"}, "config": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "size", "config"], "example": "deleted"}, "uniqueItems": true}], "title": "plan_sizes.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<PlanSizes>"}, "plans.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "plans.<PERSON>ope<PERSON><PERSON>er"}, "plans.IncludeFilter.Items": {"title": "plans.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["billingCycle", "currency", "configureDevice", "planSize", "planHistories", "softwareLevel"]}, "scope": {"$ref": "#/components/schemas/plans.ScopeFilter"}}}, "plans.Filter": {"type": "object", "title": "plans.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "description": {"type": "boolean"}, "tier": {"type": "boolean"}, "price": {"type": "boolean"}, "metaData": {"type": "boolean"}, "status": {"type": "boolean"}, "version": {"type": "boolean"}, "allowedUnlimitedUsers": {"type": "boolean"}, "costPerUser": {"type": "boolean"}, "productRefId": {"type": "boolean"}, "billingCycleId": {"type": "boolean"}, "currencyId": {"type": "boolean"}, "configureDeviceId": {"type": "boolean"}, "planSizeId": {"type": "boolean"}, "softwareLevelId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "description", "tier", "price", "metaData", "status", "version", "allowedUnlimitedUsers", "costPerUser", "productRefId", "billingCycleId", "currencyId", "configureDeviceId", "planSizeId", "softwareLevelId"], "example": "deleted"}, "uniqueItems": true}], "title": "plans.Fields"}, "include": {"title": "plans.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/plans.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Plan>"}, "plans.Filter1": {"type": "object", "title": "plans.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "plans.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "description": {"type": "boolean"}, "tier": {"type": "boolean"}, "price": {"type": "boolean"}, "metaData": {"type": "boolean"}, "status": {"type": "boolean"}, "version": {"type": "boolean"}, "allowedUnlimitedUsers": {"type": "boolean"}, "costPerUser": {"type": "boolean"}, "productRefId": {"type": "boolean"}, "billingCycleId": {"type": "boolean"}, "currencyId": {"type": "boolean"}, "configureDeviceId": {"type": "boolean"}, "planSizeId": {"type": "boolean"}, "softwareLevelId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "description", "tier", "price", "metaData", "status", "version", "allowedUnlimitedUsers", "costPerUser", "productRefId", "billingCycleId", "currencyId", "configureDeviceId", "planSizeId", "softwareLevelId"], "example": "deleted"}, "uniqueItems": true}], "title": "plans.Fields"}, "include": {"title": "plans.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/plans.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Plan>"}, "resources.Filter": {"type": "object", "title": "resources.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "config": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "config"], "example": "deleted"}, "uniqueItems": true}], "title": "resources.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Resource>"}, "resources.Filter1": {"type": "object", "title": "resources.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "resources.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "config": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "config"], "example": "deleted"}, "uniqueItems": true}], "title": "resources.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Resource>"}, "services.Filter": {"type": "object", "title": "services.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name"], "example": "deleted"}, "uniqueItems": true}], "title": "services.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Service>"}, "services.Filter1": {"type": "object", "title": "services.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "services.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name"], "example": "deleted"}, "uniqueItems": true}], "title": "services.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Service>"}, "strategies.Filter": {"type": "object", "title": "strategies.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "key": {"type": "boolean"}, "priority": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "key", "priority"], "example": "deleted"}, "uniqueItems": true}], "title": "strategies.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Strategy>"}, "strategies.Filter1": {"type": "object", "title": "strategies.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "strategies.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "key": {"type": "boolean"}, "priority": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "key", "priority"], "example": "deleted"}, "uniqueItems": true}], "title": "strategies.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Strategy>"}, "subscriptions.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "subscriptions.ScopeFilter"}, "subscriptions.IncludeFilter.Items": {"title": "subscriptions.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["plan"]}, "scope": {"$ref": "#/components/schemas/subscriptions.ScopeFilter"}}}, "subscriptions.Filter": {"type": "object", "title": "subscriptions.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "subscriberId": {"type": "boolean"}, "startDate": {"type": "boolean"}, "endDate": {"type": "boolean"}, "totalCost": {"type": "boolean"}, "numberOfUsers": {"type": "boolean"}, "externalSubscriptionId": {"type": "boolean"}, "priceRefId": {"type": "boolean"}, "status": {"type": "boolean"}, "trialEndDate": {"type": "boolean"}, "planId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "subscriberId", "startDate", "endDate", "totalCost", "numberOfUsers", "externalSubscriptionId", "priceRefId", "status", "trialEndDate", "planId"], "example": "deleted"}, "uniqueItems": true}], "title": "subscriptions.Fields"}, "include": {"title": "subscriptions.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/subscriptions.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Subscription>"}, "subscriptions.Filter1": {"type": "object", "title": "subscriptions.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "subscriptions.Where<PERSON><PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "subscriberId": {"type": "boolean"}, "startDate": {"type": "boolean"}, "endDate": {"type": "boolean"}, "totalCost": {"type": "boolean"}, "numberOfUsers": {"type": "boolean"}, "externalSubscriptionId": {"type": "boolean"}, "priceRefId": {"type": "boolean"}, "status": {"type": "boolean"}, "trialEndDate": {"type": "boolean"}, "planId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "subscriberId", "startDate", "endDate", "totalCost", "numberOfUsers", "externalSubscriptionId", "priceRefId", "status", "trialEndDate", "planId"], "example": "deleted"}, "uniqueItems": true}], "title": "subscriptions.Fields"}, "include": {"title": "subscriptions.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/subscriptions.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Subscription>"}, "tenant_billings_view.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "tenant_billings_view.ScopeFilter"}, "tenant_billings_view.IncludeFilter.Items": {"title": "tenant_billings_view.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["billingCustomer"]}, "scope": {"$ref": "#/components/schemas/tenant_billings_view.ScopeFilter"}}}, "tenant_billings_view.Filter": {"type": "object", "title": "tenant_billings_view.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "tenant_billings_view.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "invoiceId": {"type": "boolean"}, "invoiceStatus": {"type": "boolean"}, "billingCustomerId": {"type": "boolean"}, "amount": {"type": "boolean"}, "tax": {"type": "boolean"}, "discount": {"type": "boolean"}, "dueDate": {"type": "boolean"}, "customerId": {"type": "boolean"}, "tenantId": {"type": "boolean"}, "tenantName": {"type": "boolean"}, "invoiceNumber": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "invoiceId", "invoiceStatus", "billingCustomerId", "amount", "tax", "discount", "dueDate", "customerId", "tenantId", "tenantName", "invoiceNumber"], "example": "deleted"}, "uniqueItems": true}], "title": "tenant_billings_view.Fields"}, "include": {"title": "tenant_billings_view.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/tenant_billings_view.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<TenantBillingsView>"}}}, "servers": [{"url": "/"}]}