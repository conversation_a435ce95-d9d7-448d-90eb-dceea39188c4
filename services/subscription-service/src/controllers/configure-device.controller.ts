import {PermissionKey} from '@local/core';
import {service} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {ConfigureDevice} from '../models';
import {ConfigureDeviceRepository} from '../repositories/configure-devices.repository';
import {PlanRepository, SubscriptionRepository} from '../repositories';
import {ManageDeviceLimitService} from '../services';
import {IManageDeviceLimitService} from '../types';

const basePath = '/configure-devices';

/**
 * Controller for managing ConfigureDevice entities.
 */
export class ConfigureDeviceController {
  /**
   * Creates an instance of ConfigureDeviceController.
   * @param configureDeviceRepository - Repository for ConfigureDevice model.
   * @param planRepository - Repository for Plan model.
   * @param subscriptionRepository - Repository for Subscription model.
   * @param manageDeviceLimitService - Service for managing device limits.
   */
  constructor(
    @repository(ConfigureDeviceRepository)
    public configureDeviceRepository: ConfigureDeviceRepository,
    @repository(PlanRepository)
    public planRepository: PlanRepository,
    @repository(SubscriptionRepository)
    public subscriptionRepository: SubscriptionRepository,
    @service(ManageDeviceLimitService)
    private readonly manageDeviceLimitService: IManageDeviceLimitService,
  ) {}

  /**
   * Create a new ConfigureDevice instance.
   *
   * @param configureDevice - ConfigureDevice data excluding 'id'.
   * @returns The newly created ConfigureDevice instance.
   *
   * @authorization Requires permission: CreateConfigureDevices
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.CreateConfigureDevices],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ConfigureDevice model instance',
        content: {
          'application/json': {schema: getModelSchemaRef(ConfigureDevice)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ConfigureDevice, {
            title: 'NewConfigureDevice',
            exclude: ['id'],
          }),
        },
      },
    })
    configureDevice: Omit<ConfigureDevice, 'id'>,
  ): Promise<ConfigureDevice> {
    return this.configureDeviceRepository.create(configureDevice);
  }

  /**
   * Retrieve an array of ConfigureDevice instances.
   *
   * @param filter - Optional filter for querying ConfigureDevices.
   * @returns Array of ConfigureDevice instances.
   *
   * @authorization Requires permission: ViewConfigureDevices
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewConfigureDevices],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of ConfigureDevice model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ConfigureDevice, {
                includeRelations: true,
              }),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(ConfigureDevice) filter?: Filter<ConfigureDevice>,
  ): Promise<ConfigureDevice[]> {
    return this.configureDeviceRepository.find(filter);
  }

  /**
   * Update multiple ConfigureDevice instances matching the where condition.
   *
   * @param configureDevices - Partial ConfigureDevice data for update.
   * @param where - Optional filter criteria to select records to update.
   * @returns Count of updated records.
   *
   * @authorization Requires permission: UpdateConfigureDevices
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.UpdateConfigureDevices],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ConfigureDevice PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ConfigureDevice, {partial: true}),
        },
      },
    })
    configureDevices: ConfigureDevice,
    @param.where(ConfigureDevice) where?: Where<ConfigureDevice>,
  ): Promise<Count> {
    return this.configureDeviceRepository.updateAll(configureDevices, where);
  }

  /**
   * Delete ConfigureDevice instance by ID.
   *
   * @param id - The ID of the ConfigureDevice to delete.
   *
   * @authorization Requires permission: DeleteConfigureDevices
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.DeleteConfigureDevices],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @del(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'ConfigureDevice DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.configureDeviceRepository.deleteById(id);
  }
  /**
   * Upsert (insert or update) an array of ConfigureDevice instances.
   *
   * @param devices - Array of ConfigureDevice objects.
   * @returns Array of upserted ConfigureDevice instances.
   *
   * @authorization Requires permission: UpdateConfigureDevices
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.UpdateConfigureDevices],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/upsert-batch`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of upserted ConfigureDevice model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ConfigureDevice),
            },
          },
        },
      },
    },
  })
  async upsertBatch(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: getModelSchemaRef(ConfigureDevice, {partial: true}),
          },
        },
      },
    })
    devices: Partial<ConfigureDevice>[],
  ): Promise<{success: boolean; error?: string}> {
    return this.manageDeviceLimitService.upsertDeviceBatch(devices);
  }
}
