import {Permission<PERSON><PERSON>} from '@local/core';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {getModelSchemaRef, post, requestBody} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC, STATUS_CODE} from '@sourceloop/core';
import {
  AddressDto,
  ChargeDto,
  InvoiceDto,
} from '@sourceloop/ctrl-plane-subscription-service';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {BillingComponentBindings, IService} from 'loopback4-billing';
import {Invoice} from '../models';
import {InvoiceRepository} from '../repositories';
import {BillingCustomerRepository} from '../repositories/billing-customer.repository';

const basePath = '/billing-invoice';

/**
 * Controller responsible for handling billing invoice related operations.
 */
export class BillingInvoiceController {
  constructor(
    @repository(BillingCustomerRepository)
    public billingCustomerRepository: BillingCustomerRepository,
    @repository(InvoiceRepository)
    public invoiceRepository: InvoiceRepository<Invoice>,
    @inject(BillingComponentBindings.BillingProvider)
    private readonly billingProvider: IService,
  ) {}

  /**
   * Creates a new billing invoice.
   *
   * @param {Omit<InvoiceDto, 'id' | 'status'>} invoiceDto The invoice data excluding id and status
   * @returns {Promise<InvoiceDto>} The created invoice data with generated ID and status
   * @throws {Error} When customer with given tenantId is not found
   */
  @authorize({
    permissions: [PermissionKey.CreateBillingInvoice],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'invoice model instance',
        content: {'application/json': {schema: getModelSchemaRef(InvoiceDto)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(InvoiceDto, {
            title: 'newInvoice',
            exclude: ['id', 'status'],
          }),
        },
      },
    })
    invoiceDto: Omit<InvoiceDto, 'id' | 'status'>,
  ): Promise<InvoiceDto> {
    const customer = await this.billingCustomerRepository.find({
      where: {customerId: invoiceDto.customerId},
    });

    if (customer.length === 0) {
      throw new Error(' Customer with tenantId is not present');
    }
    const invoice = await this.billingProvider.createInvoice(invoiceDto);
    const charges = invoice.charges?.map(
      charge =>
        new ChargeDto({amount: charge.amount, description: charge.description}),
    );

    return new InvoiceDto({
      id: invoice.id, // passed the id of invoice info created in our db, to setup relation between subscription and invoice
      customerId: invoice.customerId,
      charges: charges,
      status: invoice.status,
      shippingAddress: new AddressDto({
        firstName: invoice.shippingAddress?.firstName ?? '',
        lastName: invoice.shippingAddress?.lastName ?? '',
        email: invoice.shippingAddress?.email ?? '',
        company: invoice.shippingAddress?.company,
        phone: invoice.shippingAddress?.phone,
        city: invoice.shippingAddress?.city ?? '',
        state: invoice.shippingAddress?.state ?? '',
        zip: invoice.shippingAddress?.zip ?? '',
        country: invoice.shippingAddress?.country ?? '',
      }),
      options: invoice.options,
      currencyCode: invoice.currencyCode,
    });
  }
}
