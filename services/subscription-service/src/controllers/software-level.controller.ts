import {Permission<PERSON><PERSON>} from '@local/core';
import {Filter, repository} from '@loopback/repository';
import {get, getModelSchemaRef, param} from '@loopback/rest';
import {OPERATION_SECURITY_SPEC} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {SoftwareLevel} from '../models';
import {SoftwareLevelRepository} from '../repositories';

const basePath = '/software-level';

/**
 * Controller for managing SoftwareLevel records related to Plans.
 */
export class SoftwareLevelController {
  /**
   * Creates an instance of SoftwareLevelController.
   * @param softwareLevelRepository - Repository for SoftwareLevel model.
   */
  constructor(
    @repository(SoftwareLevelRepository)
    protected softwareLevelRepository: SoftwareLevelRepository,
  ) {}

  /**
   * Retrieves an array of SoftwareLevel instances matching the optional filter.
   *
   * @param filter - Optional filter object to refine the search.
   * @returns Array of SoftwareLevel records.
   *
   * @authorization Requires permission: ViewSoftwareLevel
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewSoftwareLevel],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'Array of Plan has many SoftwareLevel',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SoftwareLevel)},
          },
        },
      },
    },
  })
  async find(
    @param.query.object('filter') filter?: Filter<SoftwareLevel>,
  ): Promise<SoftwareLevel[]> {
    return this.softwareLevelRepository.find(filter);
  }
}
