import {Get<PERSON>, inject} from '@loopback/core';
import {<PERSON><PERSON><PERSON>, juggler} from '@loopback/repository';
import {
  DefaultUserModifyCrudRepository,
  IAuthUserWithPermissions,
} from '@sourceloop/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {SoftwareLevel} from '../models';
import {SubscriptionDbSourceName} from '@sourceloop/ctrl-plane-subscription-service';

/**
 * Repository class for the {@link SoftwareLevel} model.
 *
 * @typeParam T - Extends {@link SoftwareLevel}, default is `SoftwareLevel`.
 *
 * @remarks
 * - Extends `DefaultUserModifyCrudRepository` to include auditing fields like
 *   createdBy, modifiedBy, etc.
 * - Provides CRUD operations for the `SoftwareLevel` entity.
 */
export class SoftwareLevelRepository<
  T extends SoftwareLevel = SoftwareLevel,
> extends DefaultUserModifyCrudRepository<
  T,
  typeof SoftwareLevel.prototype.id,
  {}
> {
  /**
   * Creates an instance of SoftwareLevelRepository.
   *
   * @param dataSource - The datasource instance for database operations.
   * @param getCurrentUser - Getter for the currently authenticated user with permissions.
   * @param softwareLevel - The SoftwareLevel model class reference.
   */
  constructor(
    @inject(`datasources.${SubscriptionDbSourceName}`)
    dataSource: juggler.DataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @inject('models.SoftwareLevel')
    private readonly softwareLevel: typeof Entity & {prototype: T},
  ) {
    super(softwareLevel, dataSource, getCurrentUser);
  }
}
