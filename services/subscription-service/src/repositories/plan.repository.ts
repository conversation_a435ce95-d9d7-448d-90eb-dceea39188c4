import {inject, Getter} from '@loopback/context';
import {
  BelongsToAccessor,
  juggler,
  repository,
  Entity,
  HasManyRepositoryFactory,
} from '@loopback/repository';
import {DefaultTransactionalUserModifyRepository} from '@sourceloop/core';
import {
  BillingCycle,
  Currency,
  SubscriptionDbSourceName,
  BillingCycleRepository,
  CurrencyRepository,
  PlanSizes,
  PlanSizesRepository,
} from '@sourceloop/ctrl-plane-subscription-service';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {
  ConfigureDevice,
  Plan,
  PlanHistory,
  PlanRelations,
  SoftwareLevel,
} from '../models';
import {ConfigureDeviceRepository} from './configure-devices.repository';
import {PlanHistoryRepository} from './plan-history.repository';
import {SoftwareLevelRepository} from './software-level.repository';

export class PlanRepository<
  T extends Plan = Plan,
> extends DefaultTransactionalUserModifyRepository<
  T,
  typeof Plan.prototype.id,
  PlanRelations
> {
  public readonly billingCycle: BelongsToAccessor<
    BillingCycle,
    typeof Plan.prototype.id
  >;

  public readonly configureDevice: BelongsToAccessor<
    ConfigureDevice,
    typeof Plan.prototype.id
  >;

  public readonly currency: BelongsToAccessor<
    Currency,
    typeof Plan.prototype.id
  >;

  public readonly planSize: BelongsToAccessor<
    PlanSizes,
    typeof Plan.prototype.id
  >;

  public readonly planHistories: HasManyRepositoryFactory<
    PlanHistory,
    typeof Plan.prototype.id
  >;

  public readonly softwareLevel: BelongsToAccessor<
    SoftwareLevel,
    typeof Plan.prototype.id
  >;

  constructor(
    @inject(`datasources.${SubscriptionDbSourceName}`)
    dataSource: juggler.DataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('BillingCycleRepository')
    protected billingCycleRepositoryGetter: Getter<BillingCycleRepository>,
    @repository.getter('CurrencyRepository')
    protected currencyRepositoryGetter: Getter<CurrencyRepository>,
    @repository.getter('ConfigureDeviceRepository')
    protected configureDeviceRepositoryGetter: Getter<ConfigureDeviceRepository>,
    @repository.getter('PlanSizesRepository')
    protected planSizesRepositoryGetter: Getter<PlanSizesRepository>,
    @inject('models.Plan')
    private readonly plan: typeof Entity & {prototype: T},
    @repository.getter('PlanHistoryRepository')
    protected planHistoryRepositoryGetter: Getter<PlanHistoryRepository>,
    @repository.getter('SoftwareLevelRepository')
    protected softwareLevelRepositoryGetter: Getter<SoftwareLevelRepository>,
  ) {
    super(plan, dataSource, getCurrentUser);
    this.softwareLevel = this.createBelongsToAccessorFor(
      'softwareLevel',
      softwareLevelRepositoryGetter,
    );
    this.registerInclusionResolver(
      'softwareLevel',
      this.softwareLevel.inclusionResolver,
    );
    this.planHistories = this.createHasManyRepositoryFactoryFor(
      'planHistories',
      planHistoryRepositoryGetter,
    );
    this.registerInclusionResolver(
      'planHistories',
      this.planHistories.inclusionResolver,
    );
    this.currency = this.createBelongsToAccessorFor(
      'currency',
      currencyRepositoryGetter,
    );
    this.billingCycle = this.createBelongsToAccessorFor(
      'billingCycle',
      billingCycleRepositoryGetter,
    );

    this.planSize = this.createBelongsToAccessorFor(
      'planSize',
      this.planSizesRepositoryGetter,
    );

    this.registerInclusionResolver('planSize', this.planSize.inclusionResolver);
    this.configureDevice = this.createBelongsToAccessorFor(
      'configureDevice',
      this.configureDeviceRepositoryGetter,
    );
    this.registerInclusionResolver(
      'configureDevice',
      this.configureDevice.inclusionResolver,
    );

    this.registerInclusionResolver(
      'billingCycle',
      this.billingCycle.inclusionResolver,
    );
    this.registerInclusionResolver('currency', this.currency.inclusionResolver);
  }
}
