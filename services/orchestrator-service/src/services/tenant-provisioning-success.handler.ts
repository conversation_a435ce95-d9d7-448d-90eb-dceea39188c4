import {TenantProvisioningSuccessHandler} from '@sourceloop/ctrl-plane-orchestrator-service';
import {injectable, BindingScope, Provider} from '@loopback/core';
import {AnyObject} from '@loopback/repository';

import * as crypto from 'crypto';
import axios from 'axios';

@injectable({scope: BindingScope.TRANSIENT})
export class TenantProvisioningSuccessHandlerProvider
  implements Provider<TenantProvisioningSuccessHandler>
{
  constructor() {}

  value() {
    return async (body: AnyObject) => {
      console.info('tenant provision success body---------', body); // NOSONAR

      const SECRET_KEY = process.env.SECRET_KEY ?? ''; // 32 bytes (AES-256)
      const IV = crypto.randomBytes(16); // initialization vector
      const cipherAlgorithm = 'aes-256-cbc'; // AES algorithm
      const AUTH_API_KEY = process.env.AUTH_API_KEY ?? '';
      try {
        console.log('IV:---', IV); //NOSONAR
        console.log('SECRET_KEY:---', SECRET_KEY); //NOSONAR

        const cipher = crypto.createCipheriv(
          cipherAlgorithm, // NOSONAR
          Buffer.from(SECRET_KEY, 'base64'),
          IV,
        );
        let encryptedKey = cipher.update(AUTH_API_KEY, 'utf-8', 'hex');
        encryptedKey += cipher.final('hex');
        const encryptedApiKey = `${IV.toString('hex')}:${encryptedKey}`;
        console.log('Encrypted API Key:----', encryptedApiKey); //NOSONAR

        // Extract plan and builder information from the body
        const tenant = body.tenant; // NOSONAR
        console.info('Tenant:', tenant); // NOSONAR

        // Prepare webhook request
        const webhookHost = process.env.WEBHOOK_HOST ?? '';
        const webhookUrl = `${webhookHost}/webhook`;
        console.info('Webhook URL:', webhookUrl); // NOSONAR
        try {
          const reqBody = {
            id: body.TENANT_ID,
            status:
              body.CODEBUILD_BUILD_SUCCEEDING === 1
                ? 'ACTIVE'
                : 'PROVISIONFAILED',
          };
          console.info('Request Body:', reqBody); // NOSONAR
          const response = await axios.post(webhookUrl, reqBody, {
            headers: {
              'x-api-key': encryptedApiKey,
              'Content-Type': 'application/json',
            },
            timeout: 5000, // NOSONAR
          });
          console.info('Webhook response status:', response.status); // NOSONAR
          console.info('Webhook response :', response); // NOSONAR

          if (!response?.status) {
            throw new Error('No response from webhook'); // NOSONAR
          }
          if (response.status === 200) {
            // NOSONAR
            console.log('Tenant status updated and notified to tenant admin'); // NOSONAR
          } else {
            console.error('Tenant status update failed'); // NOSONAR
          }
        } catch (err) {
          console.error('Tenant status update failed', err); // NOSONAR
        }
      } catch (err) {
        console.error('Error for sending request:', err); // NOSONAR
      }
    };
  }
}
