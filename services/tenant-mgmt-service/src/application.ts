import {
  recordRequestMetrics,
  AuthorizeActionProvider,
  CryptoHelperService,
} from '@local/core';
import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import * as dotenv from 'dotenv';
import * as dotenvExt from 'dotenv-extended';
import {AuthenticationComponent} from 'loopback4-authentication';
import {
  AuthorizationBindings,
  AuthorizationComponent,
} from 'loopback4-authorization';
import {
  ServiceSequence,
  SFCoreBindings,
  BearerVerifierBindings,
  BearerVerifierComponent,
  BearerVerifierConfig,
  BearerVerifierType,
  SECURITY_SCHEME_SPEC,
} from '@sourceloop/core';

import {RepositoryMixin} from '@loopback/repository';
import {Middleware, RestApplication} from '@loopback/rest';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'path';
import * as openapi from './openapi.json';
import {
  EventConnectorBinding,
  TenantManagementServiceBindings,
  TenantManagementServiceComponent,
  SYSTEM_USER,
} from '@sourceloop/ctrl-plane-tenant-management-service';

import {EventConnector} from './services/event.service';
import {ApiKeyInterceptor} from './interceptors/api-key.interceptor';

export {ApplicationConfig};

export class TenantMgmtServiceApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    const port = 3000;
    dotenv.config();
    dotenvExt.load({
      schema: '.env.example',
      errorOnMissing: process.env.NODE_ENV !== 'test',
      includeProcessEnv: true,
    });
    options.rest = options.rest ?? {};
    options.rest.basePath = process.env.BASE_PATH ?? '';
    options.rest.port = +(process.env.PORT ?? port);
    options.rest.host = process.env.HOST;
    options.rest.openApiSpec = {
      endpointMapping: {
        [`${options.rest.basePath}/openapi.json`]: {
          version: '3.0.0',
          format: 'json',
        },
      },
    };

    super(options);

    // To check if monitoring is enabled from env or not
    const enableObf = !!+(process.env.ENABLE_OBF ?? 0);
    // To check if authorization is enabled for swagger stats or not
    const authentication = !!(
      process.env.SWAGGER_USER && process.env.SWAGGER_PASSWORD
    );
    const obj = {
      enableObf,
      obfPath: process.env.OBF_PATH ?? '/obf',
      openapiSpec: openapi,
      authentication: authentication,
      swaggerUsername: process.env.SWAGGER_USER,
      swaggerPassword: process.env.SWAGGER_PASSWORD,
    };
    this.bind(SFCoreBindings.config).to(obj);

    // Set up the custom sequence
    this.sequence(ServiceSequence);
    this.bind(TenantManagementServiceBindings.Config).to({
      useCustomSequence: true,
    });

    // Add authentication component
    this.component(AuthenticationComponent);
    this.bind(EventConnectorBinding).toClass(EventConnector);

    this.component(TenantManagementServiceComponent);

    // Add bearer verifier component
    this.bind(BearerVerifierBindings.Config).to({
      type: BearerVerifierType.service,
      useSymmetricEncryption: true,
    } as BearerVerifierConfig);
    this.component(BearerVerifierComponent);
    // Add authorization component
    this.bind(AuthorizationBindings.CONFIG).to({
      allowAlwaysPaths: ['/explorer', '/openapi.json'],
    });
    this.component(AuthorizationComponent);
    this.bind(AuthorizationBindings.AUTHORIZE_ACTION.key).toProvider(
      AuthorizeActionProvider,
    );
    // Set up default home page
    this.bind('services.CryptoHelperServiceCustom').toClass(
      CryptoHelperService,
    );
    this.static('/', path.join(__dirname, '../public'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });

    this.bind(SYSTEM_USER).to({
      username: 'system',
      id: process.env.SYSTEM_USER_ID,
      userTenantId: process.env.SYSTEM_USER_TENANT_ID,
      permissions: [],
    });
    this.component(RestExplorerComponent);

    // Register tracing middleware
    // this.middleware(recordRequestMetrics);
    // Wrap Express middleware into LB4 middleware
    const metricsMiddleware: Middleware = async (ctx, next) => {
      const {request, response} = ctx;
      return new Promise<void>((resolve, reject) => {
        recordRequestMetrics(request, response, async () => {
          try {
            await next();
            resolve();
          } catch (err) {
            reject(err);
          }
        });
      });
    };

    this.middleware(metricsMiddleware);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
    };

    this.api({
      openapi: '3.0.0',
      info: {
        title: 'tenant-mgmt-service',
        version: '1.0.0',
      },
      paths: {},
      components: {
        securitySchemes: SECURITY_SCHEME_SPEC,
      },
      servers: [{url: '/'}],
    });

    // Register API Key Interceptor for controller usage
    this.bind('ApiKeyInterceptor').toProvider(ApiKeyInterceptor);
  }
}
