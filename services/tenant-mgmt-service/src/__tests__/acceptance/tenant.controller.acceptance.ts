import {Client, expect} from '@loopback/testlab';
import {TenantMgmtServiceApplication} from '../..';
import {getRepo, getToken, setupApplication} from './test-helper';
import {ILogger, LOGGER, STATUS_CODE} from '@sourceloop/core';
import {BindingScope} from '@loopback/context';
import {TenantRepository} from '../../repositories';
import {
  ContactRepository,
  Tenant,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {PermissionKey} from '@local/core';
import {mockTenant, mockTenantOnboardDTO, mockContact} from './mock-data';

describe('TenantController', () => {
  let app: TenantMgmtServiceApplication;
  let client: Client;
  let tenantRepo: TenantRepository;
  let contactRepo: ContactRepository;
  const key32Bytes = 'abcdefghijklmnopqrstuvwxyz123456';
  process.env.SECRET_KEY = Buffer.from(key32Bytes).toString('base64');
  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
    tenantRepo = await getRepo(app, 'repositories.TenantRepository');
    contactRepo = await getRepo(app, 'repositories.ContactRepository');

    const logger = app.getSync<ILogger>(LOGGER.LOGGER_INJECT);
    app.bind(LOGGER.LOGGER_INJECT).to(logger).inScope(BindingScope.SINGLETON);
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    await seedData();
  });

  afterEach(async () => {
    await tenantRepo.deleteAllHard();
  });

  it('invokes POST /tenants with valid token', async () => {
    const token = getToken([PermissionKey.CreateTenant]);
    const {body} = await client
      .post('/tenants')
      .set('Authorization', token)
      .send({...mockTenantOnboardDTO})
      .expect(STATUS_CODE.OK);
    expect(body.id).to.be.String();
    expect(body.name).to.eql(mockTenantOnboardDTO.name);

    // should create the contact as well
    const contact = await contactRepo.findOne({
      where: {email: mockTenantOnboardDTO.contact?.email},
    });
    expect(contact).to.not.be.null();
  });

  it('invokes POST /tenants/verify-key and returns available=true when key does not exist', async () => {
    const token = getToken([PermissionKey.ViewTenant]);
    const {body} = await client
      .post('/tenants/verify-key')
      .set('Authorization', token)
      .send({key: 'nonexistingkey'})
      .expect(STATUS_CODE.OK);

    expect(body.available).to.be.true();
    expect(body.suggestions).to.be.undefined();
  });
  //Sunny / kannan chk this . throwing error so commented
  // it('invokes POST /tenants/verify-key and returns available=false with suggestions when key already exists', async () => {
  //   const token = getToken([PermissionKey.ViewTenant]);

  //   // Ensure tenant with a known key exists (already seeded in beforeEach)
  //   const existingKey = mockTenant.key;

  //   const {body} = await client
  //     .post('/tenants/verify-key')
  //     .set('Authorization', token)
  //     .send({key: existingKey})
  //     .expect(STATUS_CODE.OK);

  //expect(body.available).to.be.false();
  // expect(body.suggestions).to.be.an.Array();
  // expect(body.suggestions.length).to.be.greaterThan(0);

  // suggestions should not contain the original key
  //   body.suggestions.forEach((suggestion: string) => {
  //     expect(suggestion).to.startWith(existingKey ?? '');
  //     expect(suggestion).to.not.equal(existingKey);
  //   });
  // });

  async function seedData() {
    await tenantRepo.create({
      ...mockTenant,
      key: mockTenant.key,
    });
  }

  // Test case for GET /tenants/count
  it('invokes GET /tenants/count and returns the count of tenants', async function () {
    const token = getToken([PermissionKey.ViewTenant]);
    const {body} = await client
      .get('/tenants/count')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body.count).to.be.a.Number();
  });

  // Test case for GET /tenants
  it('invokes GET /tenants and returns an array of tenant instances', async function () {
    const token = getToken([PermissionKey.ViewTenant]);
    const {body} = await client
      .get('/tenants')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.an.Array();
    body.forEach((tenant: Partial<Tenant>) => {
      expect(tenant).to.have.properties(['id', 'name', 'key']);
    });
  });

  // Test case for GET /tenants/{id}
  it('invokes GET /tenants/{id} and returns a tenant instance with relations', async function () {
    const token = getToken([PermissionKey.ViewTenant]);

    // First create a tenant to retrieve
    const createdTenant = await tenantRepo.create({
      ...mockTenant,
      key: 'test-get-by-id',
    });

    // Create a contact for this tenant
    const createdContact = await contactRepo.create({
      ...mockContact,
      tenantId: createdTenant.id,
      email: '<EMAIL>',
    });

    const {body} = await client
      .get(`/tenants/${createdTenant.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    // Verify basic tenant properties
    expect(body).to.have.properties(['id', 'name', 'key', 'status', 'domains']);
    expect(body.id).to.eql(createdTenant.id);
    expect(body.name).to.eql(mockTenant.name);
    expect(body.key).to.eql('test-get-by-id');
    expect(body.status).to.eql(mockTenant.status);

    // Verify relations are included
    expect(body).to.have.property('contacts');
    expect(body.contacts).to.be.an.Array();
    expect(body.contacts.length).to.be.greaterThan(0);

    // Verify contact details
    const contact = body.contacts.find(
      (c: {id: string}) => c.id === createdContact.id,
    );
    expect(contact).to.not.be.undefined();
    expect(contact.email).to.eql('<EMAIL>');
    expect(contact.firstName).to.eql(mockContact.firstName);
    expect(contact.lastName).to.eql(mockContact.lastName);

    // Note: files and address might not be present if not created,
    // but the endpoint should at least include them as properties if relations are requested
    // The actual presence depends on the data model implementation
  });

  // Test case for GET /tenants/{id} with invalid ID
  it('invokes GET /tenants/{id} with invalid ID and returns 404', async function () {
    const token = getToken([PermissionKey.ViewTenant]);
    const invalidId = '99999999-9999-9999-9999-999999999999';

    await client
      .get(`/tenants/${invalidId}`)
      .set('Authorization', token)
      .expect(404);
  });

  it('invokes GET /all-status/metrics and returns dashboard metrics', async function () {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const {body} = await client
      .get('/tenants/all-status/metrics')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    const statues = ['0', '1', '2', '3', '4', '5'];
    expect(body).to.be.an.Object();
    expect(body).to.have.properties(statues);
    statues.forEach(key => {
      expect(body[key]).to.have.properties(['count', 'status']);
    });
    const statusesName = [
      'ACTIVE',
      'PENDINGPROVISION',
      'PROVISIONING',
      'PROVISIONFAILED',
      'INACTIVE',
    ];
    statusesName.forEach((status, index) => {
      expect(body[index].status).to.eql(status);
      expect(body[index].count).to.eql(index === 0 ? 1 : 0);
    });
  });
});
