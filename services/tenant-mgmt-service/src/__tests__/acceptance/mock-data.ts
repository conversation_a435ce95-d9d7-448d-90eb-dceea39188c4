import {DataObject} from '@loopback/repository';
import {Contact, Tenant, TenantOnboardDTO} from '../../models';
import {
  Address,
  Invoice,
  InvoiceStatus,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {TenantStatus} from '@local/core';

export const testAddress = 'test-address';
export const testState = 'test-state';

export const mockAddress: DataObject<Address> = {
  address: testAddress,
  city: 'test-city',
  state: testState,
  country: 'test-country',
  zip: 'test-zip',
};

export const mockInvoice: DataObject<Invoice> = {
  amount: 100,
  currencyCode: 'USD',
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  dueDate: '2024-02-15',
  status: InvoiceStatus.PENDING,
  tenantId: 'test-tenant-id',
};

export const testDomain = 'contact.com';

export const mockContact: DataObject<Contact> = {
  firstName: 'testcontact',
  lastName: 'dev',
  userName: 'testuser',
  email: '<EMAIL>',
  phoneNumber: '1234567890',
  countryCode: '+91',
  designation: 'dev',
  isPrimary: false,
};

export const mockTenantOnboardDTO: DataObject<TenantOnboardDTO> = {
  contact: mockContact,
  name: 'testname',
  country: 'India',
  address: testAddress,
  city: 'test-city',
  state: testState,
  zip: 'test-zip',
  key: 'testkey',
  domains: [testDomain],
  lang: 'English',
  files: [],
};
export const mockSubscriptionId = 'test-subscription-id';

export const DateFormat = 'YYYY-MM-DD';

export const mockTenant: DataObject<Tenant> = {
  name: 'test-name',
  status: TenantStatus.ACTIVE,
  spocUserId: 'test-user-id',
  key: 'test',
  domains: [testDomain],
};

export const mockDto: TenantOnboardDTO = new TenantOnboardDTO({
  name: 'test-name',
  country: 'India',
  address: testAddress,
  city: 'test-city',
  state: 'test-state',
  zip: 'test-zip',
  key: 'testkey',
  domains: [testDomain],
});
