import {expect, sinon} from '@loopback/testlab';
import {
  Contact,
  FileObject,
  Tenant,
  TenantOnboardDTO,
  CreateLeadDTO,
  Lead,
} from '../../models';
import {
  FileRepository,
  LeadRepository,
  TenantRepository,
} from '../../repositories';
import {OnboardingService} from '../../services';
import {
  Address,
  AddressRepository,
  ContactRepository,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {
  StorageSource,
  TenantStatus,
  LeadStatus,
  CryptoHelperService,
} from '@local/core';
import {ILogger} from '@sourceloop/core';
import {Transaction} from '../fixtures';

describe('OnboardingService (unit)', () => {
  let onboardingService: OnboardingService;
  let tenantRepository: sinon.SinonStubbedInstance<TenantRepository>;
  let leadRepository: sinon.SinonStubbedInstance<LeadRepository>;

  let contactRepository: sinon.SinonStubbedInstance<ContactRepository<Contact>>;
  let addressRepository: sinon.SinonStubbedInstance<AddressRepository>;
  let fileRepository: sinon.SinonStubbedInstance<FileRepository>;
  let logger: sinon.SinonStubbedInstance<ILogger>;
  let cryptoHelperServiceStub: sinon.SinonStubbedInstance<CryptoHelperService>;

  let transactionStub: sinon.SinonStubbedInstance<Transaction>;

  beforeEach(() => {
    tenantRepository = sinon.createStubInstance(TenantRepository);
    contactRepository = sinon.createStubInstance(ContactRepository);
    addressRepository = sinon.createStubInstance(AddressRepository);
    fileRepository = sinon.createStubInstance(FileRepository);
    leadRepository = sinon.createStubInstance(LeadRepository);
    cryptoHelperServiceStub = sinon.createStubInstance(CryptoHelperService);

    transactionStub = sinon.createStubInstance(Transaction);

    // Set up stubbed methods
    transactionStub.commit.resolves();
    transactionStub.rollback.resolves();
    transactionStub.isActive.returns(true);

    (tenantRepository.beginTransaction as sinon.SinonStub).resolves(
      transactionStub,
    );

    logger = {
      log: sinon.stub(),
      info: sinon.stub(),
      warn: sinon.stub(),
      error: sinon.stub(),
      debug: sinon.stub(),
    } as unknown as sinon.SinonStubbedInstance<ILogger>;

    onboardingService = new OnboardingService(
      tenantRepository,
      contactRepository,
      addressRepository,
      fileRepository,
      leadRepository,
      logger,
      cryptoHelperServiceStub,
    );
  });

  const dtoMock: TenantOnboardDTO = new TenantOnboardDTO({
    key: 'tenant-key',
    name: 'Test Tenant',
    domains: ['test.com'],
    country: 'IN',
    address: '123 Street',
    city: 'Delhi',
    state: 'DL',
    zip: '110001',
    lang: 'en',
    contact: new Contact({
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      isPrimary: true,
      countryCode: '+91',
      phoneNumber: '9999999999',
      designation: 'Manager',
      userName: 'johndoe',
    }),
    files: [
      new FileObject({
        fileKey: 'file1',
        originalName: 'test.pdf',
        source: StorageSource.S3,
        size: 200,
      }),
    ],
  });

  const tenantMock: Tenant = new Tenant({
    id: 'tenant123',
    key: dtoMock.key,
    name: dtoMock.name,
    domains: dtoMock.domains,
    status: TenantStatus.PENDINGPROVISION,
    addressId: 'address123',
    contacts: [],
    resources: [],
    lang: 'English',
    files: [],
  });

  const addressMock = {
    id: 'address123',
    address: '123 Street',
    city: 'Delhi',
    state: 'DL',
    country: 'IN',
    zip: '110001',
  };

  /**
   * ✅ Test 2: Successful onboarding without lead (creates new address)
   */
  it('creates a new address if no lead provided', async () => {
    addressRepository.create.resolves(addressMock as unknown as Address);
    tenantRepository.create.resolves(tenantMock);
    tenantRepository.findById.resolves(tenantMock);

    const res = await onboardingService.onboard(dtoMock);

    expect(addressRepository.create.calledOnce).to.be.true();
    expect(tenantRepository.create.calledOnce).to.be.true();
    expect(transactionStub.commit.calledOnce).to.be.true();
    expect(res).to.eql(tenantMock);
  });

  it('throws error if contact with email exists', async () => {
    contactRepository.findOne.resolves(
      new Contact({email: dtoMock.contact.email}),
    );
    await expect(onboardingService.onboard(dtoMock)).to.be.rejectedWith(
      `Contact with email ${dtoMock.contact.email} already exists`,
    );
  });

  it('throws error if leadId is provided but not found', async () => {
    contactRepository.findOne.resolves(null);
    addressRepository.create.resolves(addressMock as unknown as Address);
    tenantRepository.create.resolves(tenantMock);
    leadRepository.findById.rejects(new Error('Not Found'));
    await expect(
      onboardingService.onboard(dtoMock, 'invalid-lead-id'),
    ).to.be.rejectedWith('Lead with id invalid-lead-id not found');
  });

  it('updates lead status to CONVERTED when onboarding with valid lead', async () => {
    contactRepository.findOne.resolves(null);
    addressRepository.create.resolves(addressMock as unknown as Address);
    tenantRepository.create.resolves(tenantMock);
    tenantRepository.findById.resolves(tenantMock);
    leadRepository.findById.resolves(new Lead({id: 'lead1'}));
    leadRepository.updateById.resolves();
    await onboardingService.onboard(dtoMock, 'lead1');
    expect(
      leadRepository.updateById.calledWith('lead1', {
        status: LeadStatus.CONVERTED,
      }),
    ).to.be.true();
  });

  /**
   * ✅ Test 5: Rolls back if tenantRepository.create fails
   */
  it('rolls back transaction if tenantRepository.create throws', async () => {
    addressRepository.create.resolves(addressMock as unknown as Address);
    tenantRepository.create.rejects(new Error('DB error'));

    await expect(onboardingService.onboard(dtoMock)).to.be.rejectedWith(
      'DB error',
    );
    expect(transactionStub.rollback.calledOnce).to.be.true();
  });
  /**
   * ✅ Test: addLead creates a new lead and address, commits transaction
   */
  it('creates a new lead and address, commits transaction', async () => {
    const leadInput = new CreateLeadDTO({
      companyName: 'TestCorp',
      email: '<EMAIL>',
      firstName: 'Alice',
      lastName: 'Smith',
      designation: 'CEO',
      phoneNumber: '1234567890',
      countryCode: '+1',
      address: '456 Main St',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'USA',
    });
    leadRepository.findOne.resolves(null);
    addressRepository.beginTransaction.resolves(transactionStub);
    addressRepository.create.resolves({
      id: 'addr1',
      country: 'USA',
      address: '456 Main St',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      getId: () => 'addr1',
      getIdObject: () => ({id: 'addr1'}),
      toJSON: () => ({}),
      toObject: () => ({}),
    });
    leadRepository.create.resolves(
      new Lead({
        id: 'lead1',
        addressId: 'addr1',
        companyName: leadInput.companyName,
        email: leadInput.email,
        firstName: leadInput.firstName,
        lastName: leadInput.lastName,
        designation: leadInput.designation,
        phoneNumber: leadInput.phoneNumber,
        countryCode: leadInput.countryCode,
        isValidated: false,
        tenant: undefined,
        getId: () => 'lead1',
        getIdObject: () => ({id: 'lead1'}),
        toJSON: () => ({}),
        toObject: () => ({}),
      }),
    );

    const result = await onboardingService.addLead(leadInput);

    expect(addressRepository.create.calledOnce).to.be.true();
    expect(leadRepository.create.calledOnce).to.be.true();
    expect(transactionStub.commit.calledOnce).to.be.true();
    expect(result).to.containEql({id: 'lead1', addressId: 'addr1'});
  });

  /**
   * ✅ Test: addLead throws error if lead with email exists
   */
  it('throws error if lead with email exists', async () => {
    leadRepository.findOne.resolves(
      new Lead({
        id: 'lead1',
        email: '<EMAIL>',
        companyName: 'TestCorp',
        firstName: 'Alice',
        lastName: 'Smith',
        designation: 'CEO',
        phoneNumber: '1234567890',
        countryCode: '+1',
        isValidated: false,
        tenant: undefined,
        getId: () => 'lead1',
        getIdObject: () => ({id: 'lead1'}),
        toJSON: () => ({}),
        toObject: () => ({}),
      }),
    );

    await expect(
      onboardingService.addLead(
        new CreateLeadDTO({
          companyName: 'TestCorp',
          email: '<EMAIL>',
          firstName: 'Alice',
          lastName: 'Smith',
          designation: 'CEO',
          phoneNumber: '1234567890',
          countryCode: '+1',
          address: '456 Main St',
          city: 'New York',
          state: 'NY',
          zip: '10001',
          country: 'USA',
        }),
      ),
    ).to.be.rejectedWith(
      'Pending Tenant <NAME_EMAIL> already exists',
    );
    expect(logger.error.calledOnce).to.be.true();
  });

  /**
   * ✅ Test: addLead rolls back transaction on error
   */
  it('rolls back transaction if addressRepository.create throws', async () => {
    leadRepository.findOne.resolves(null);
    addressRepository.beginTransaction.resolves(transactionStub);
    addressRepository.create.rejects(new Error('DB error'));

    await expect(
      onboardingService.addLead(
        new CreateLeadDTO({
          companyName: 'TestCorp',
          email: '<EMAIL>',
          firstName: 'Bob',
          lastName: 'Brown',
          designation: 'CTO',
          phoneNumber: '9876543210',
          countryCode: '+1',
          address: '789 Main St',
          city: 'Boston',
          state: 'MA',
          zip: '02101',
          country: 'USA',
        }),
      ),
    ).to.be.rejectedWith('DB error');
    expect(transactionStub.rollback.calledOnce).to.be.true();
  });
});
