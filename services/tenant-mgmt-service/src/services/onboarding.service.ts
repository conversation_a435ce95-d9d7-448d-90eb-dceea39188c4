import {CryptoHelperService, LeadStatus, TenantStatus} from '@local/core';
import {BindingScope, inject, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {<PERSON>ogger, LOGGER} from '@sourceloop/core';
import {
  AddressRepository,
  ContactRepository,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {Contact, CreateLeadDTO, Tenant, TenantOnboardDTO} from '../models';
import {
  FileRepository,
  LeadRepository,
  TenantRepository,
} from '../repositories';
const country = 'USA';

@injectable({scope: BindingScope.REQUEST})
export class OnboardingService {
  /**
   * Constructs a new instance of the OnboardingService.
   * @param {LeadRepository} leadRepository - Repository for managing leads.
   * @param {TenantRepository} tenantRepository - Repository for managing tenants.
   * @param {ContactRepository} contactRepository - Repository for managing contacts.
   * @param {AddressRepository} addressRepository - Repository for managing addresses.
   * @param {FileRepository} fileRepository - Repository for managing files.
   * @param {ILogger} logger - Logger service for logging messages.
   */
  constructor(
    @repository(TenantRepository)
    private tenantRepository: TenantRepository<Tenant>,
    @repository(ContactRepository)
    private contactRepository: ContactRepository<Contact>,
    @repository(AddressRepository)
    private addressRepository: AddressRepository,
    @repository(FileRepository)
    private fileRepository: FileRepository,
    @repository(LeadRepository)
    private leadRepository: LeadRepository,
    @inject(LOGGER.LOGGER_INJECT)
    private logger: ILogger,
    @inject('services.CryptoHelperServiceCustom')
    private readonly cryptoHelperService: CryptoHelperService,
  ) {}

  /**
   * Adds a new lead to the system after validating that the lead does not already exist.
   *
   * This method performs the following steps:
   * 1. Checks if a lead with the provided email already exists. If so, logs an error and throws a BadRequest error.
   * 2. Begins a database transaction.
   * 3. Creates a new address record using the provided lead details.
   * 4. Creates a new lead record associated with the newly created address.
   * 5. Commits the transaction if successful, or rolls back in case of an error.
   *
   * @param lead - The lead data to create, excluding 'isValidated', 'addressId', and 'id'.
   * @returns The newly created lead entity.
   * @throws {HttpErrors.BadRequest} If a lead with the given email already exists.
   * @throws {Error} If any error occurs during the transaction, the transaction is rolled back and the error is thrown.
   */
  async addLead(lead: Omit<CreateLeadDTO, 'isValidated' | 'addressId' | 'id'>) {
    const existing = await this.leadRepository.findOne({
      where: {
        email: lead.email,
      },
    });
    if (existing) {
      this.logger.error(`Lead with email ${lead.email} already exists`);
      throw new HttpErrors.BadRequest(
        `Pending Tenant with email ${lead.email} already exists`,
      );
    }

    const transaction = await this.addressRepository.beginTransaction();

    try {
      const address = await this.addressRepository.create(
        {
          country: lead.country ?? country,
          address: lead.address,
          city: lead.city,
          state: lead.state,
          zip: lead.zip,
        },
        {transaction},
      );

      const newLead = await this.leadRepository.create(
        {
          companyName: lead.companyName,
          email: lead.email,
          firstName: lead.firstName,
          lastName: lead.lastName,
          designation: lead.designation,
          phoneNumber: lead.phoneNumber,
          countryCode: lead.countryCode,
          addressId: address?.id,
        },
        {transaction},
      );
      await transaction.commit();
      return newLead;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  /**
   * The `setupTenant` function creates a new tenant with the provided information and an optional lead,
   * and returns the created tenant.
   * @param {TenantOnboardDTO} dto - The `dto` parameter is an object of type `TenantOnboardDTO` which
   * contains the necessary information to onboard a new tenant. It includes properties such as `key`,
   * `country`, `address`, `city`, `state`, `zip`, and `name`.
   * @param {Lead} [lead] - The `lead` parameter is an optional parameter of type `Lead`. It represents
   * the lead associated with the tenant being onboarded. If a lead is provided, their information will
   * be used to create a contact for the tenant. If no lead is provided, the contact will not be created.
   * @returns a Promise that resolves to a Tenant object.
   */
  async onboard(dto: TenantOnboardDTO, leadId?: string): Promise<Tenant> {
    const existingContact = await this.contactRepository.findOne({
      where: {email: dto.contact.email},
    });
    if (existingContact) {
      throw new HttpErrors.BadRequest(
        `Contact with email ${dto.contact.email} already exists`,
      );
    }
    const transaction = await this.tenantRepository.beginTransaction();
    try {
      let lead = undefined;
      if (leadId) {
        lead = await this.leadRepository
          .findById(leadId)
          .catch(() => undefined);
        if (!lead) {
          throw new HttpErrors.NotFound(`Lead with id ${leadId} not found`);
        }
      }

      const address = await this.addressRepository.create(
        {
          country: dto.country ?? country,
          address: dto.address,
          city: dto.city,
          state: dto.state,
          zip: dto.zip,
        },
        {transaction},
      );

      const tenant = await this.tenantRepository.create(
        {
          key: dto.key,
          name: dto.name,
          domains: dto.domains,
          status: TenantStatus.PENDINGPROVISION,
          addressId: address?.id,
          lang: dto.lang,
          planName: dto.planName,
          planId: dto.planId,
          ...(lead ? {leadId: lead.id} : null),
        },
        {transaction},
      );

      // Generate a random password and encrypt it before storing
      const randomPassword = this.cryptoHelperService.generateRandomString(16);
      const encryptedPassword =
        this.cryptoHelperService.encryptPassword(randomPassword);

      await this.contactRepository.create(
        {
          email: dto.contact.email,
          firstName: dto.contact.firstName,
          lastName: dto.contact.lastName,
          userName: dto.contact.userName,
          tenantId: tenant.id,
          isPrimary: dto.contact.isPrimary,
          countryCode: dto.contact.countryCode,
          phoneNumber: dto.contact.phoneNumber,
          designation: dto.contact.designation,
          password: encryptedPassword,
        },
        {transaction},
      );

      if (dto.files && dto.files.length > 0) {
        await Promise.all(
          dto.files.map(async file => {
            await this.fileRepository.create(
              {
                fileKey: file.fileKey,
                originalName: file.originalName,
                tenantId: tenant.id,
                source: file.source,
                size: file.size,
              },
              {transaction},
            );
          }),
        );
      }

      if (lead) {
        await this.leadRepository.updateById(
          lead.id,
          {status: LeadStatus.CONVERTED},
          {
            transaction,
          },
        );
      }

      const res = await this.tenantRepository.findById(
        tenant.id,
        {
          include: [
            {relation: 'contacts'},
            {relation: 'resources'},
            {relation: 'lead'},
            {relation: 'address'},
          ],
        },
        {transaction},
      );

      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
