import {Entity, model, property} from '@loopback/repository';
import {Contact} from '../contact.model';
import {getJsonSchema} from '@loopback/rest';
import {ALLOWED_FILE_EXTENSIONS} from '../../constant';
import {MulterS3Storage} from '@sourceloop/file-utils/s3';
import {fileProperty} from '@sourceloop/file-utils';
import {FileObject} from './file-dto.model';
@model({
  description: 'model describing payload used to create and onboard a tenant',
  settings: {multer: {limitsProvider: true}},
})
export class EditTenantDTO extends Entity {
  @property({
    type: 'object',
    itemType: 'object',
    required: true,
  })
  existingFiles: FileObject[];

  @property({
    type: 'object',
    description:
      'metadata for the contact to be created, it is required when tenant is created without a lead',
    jsonSchema: getJsonSchema(Contact, {
      exclude: ['tenantId', 'id'],
    }),
  })
  contact: Omit<Contact, 'id' | 'tenantId'>;
  @property({
    type: 'number',
    description: 'number of users for the tenant',
  })
  numberOfUsers?: number;

  @property({
    type: 'number',
    description: 'total cost of the tenant',
    required: true,
  })
  totalCost: number;

  @property({
    type: 'string',
    required: true,
  })
  planId: string;

  @fileProperty({
    type: 'array',
    itemType: 'string',
    extensions: ALLOWED_FILE_EXTENSIONS,
    storageOptions: {
      storageClass: MulterS3Storage,
      options: {
        bucket: process.env.AWS_S3_BUCKET,
      },
    },
  })
  files: Express.Multer.File[];

  @property({
    type: 'string',
    description: 'city of the tenant owner',
  })
  city?: string;

  @property({
    description: 'state of the tenant owner',
    type: 'string',
  })
  state?: string;
  constructor(data?: Partial<EditTenantDTO>) {
    super(data);
  }
}
