import {
  model,
  property,
  belongsTo,
  hasMany,
  hasOne,
} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {
  BillingCycle,
  Currency,
  PlanSizes,
} from '@sourceloop/ctrl-plane-subscription-service';
import {ConfigureDevice} from './configure-devices.model';
import {PlanHistory} from './plan-history.model';
import {PlanStatus, PlanTierType} from '@local/core';
import {SoftwareLevel} from './software-level.model';

/**
 * Plan model representing subscription plans with all their details.
 */
@model({
  name: 'plans',
})
export class Plan extends UserModifiableEntity {
  /**
   * Unique identifier for the Plan.
   */
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  /**
   * Name of the plan.
   */
  @property({
    type: 'string',
    description: 'name of the plan',
    required: true,
  })
  name: string;

  /**
   * Optional description of the plan.
   */
  @property({
    type: 'string',
    description: 'description of the plan',
  })
  description?: string;

  /**
   * Tier of the plan.
   */
  @property({
    type: 'string',
    required: true,
    description: 'Tier of the plan.',
    jsonSchema: {
      enum: Object.values(PlanTierType),
    },
  })
  tier: PlanTierType;

  /**
   * Price of the plan.
   */
  @property({
    type: 'number',
    required: true,
    jsonSchema: {
      minimum: 0, // Minimum allowed value
      maximum: 100000, // Maximum allowed value
    },
  })
  price: number;

  /**
   * Optional metadata associated with the plan.
   */
  @property({
    type: 'object',
    name: 'meta_data',
    description: 'Meta data of the plan',
  })
  metaData?: object;

  /**
   * Status of the plan.
   */
  @property({
    type: 'number',
    description: 'Status of the plan.',
    required: true,
    jsonSchema: {
      enum: Object.values(PlanStatus).filter(v => typeof v === 'number'),
    },
  })
  status: PlanStatus;

  @property({
    type: 'string',
    description: 'product reference ID of the plan.',
    required: true,
    name: 'product_ref_id',
  })
  productRefId: string;

  /**
   * Current version string of the plan.
   */
  @property({
    type: 'string',
    description: 'current version of the plan.',
    required: true,
  })
  version: string;

  /**
   * Indicates if the plan allows unlimited users.
   */
  @property({
    type: 'boolean',
    description: 'Indicates if the plan allows unlimited users.',
    name: 'allowed_unlimited_users',
  })
  allowedUnlimitedUsers: boolean;

  /**
   * Price added per user for the plan, if applicable.
   */
  @property({
    type: 'number',
    description: 'Price added per user for the plan.',
    name: 'cost_per_user',
    jsonSchema: {
      minimum: 0, // Min boundary
      maximum: 1000, // Max boundary
    },
  })
  costPerUser?: number;

  /**
   * Foreign key referencing the associated BillingCycle.
   */
  @belongsTo(
    () => BillingCycle,
    {
      keyTo: 'id',
    },
    {
      name: 'billing_cycle_id',
    },
  )
  billingCycleId: string;

  @belongsTo(
    () => SoftwareLevel,
    {keyTo: 'id'},
    {
      name: 'software_level_id',
    },
  )
  softwareLevelId: string;

  /**
   * Foreign key referencing the associated Currency.
   */
  @belongsTo(() => Currency, undefined, {
    name: 'currency_id',
  })
  currencyId: string;

  /**
   * Foreign key referencing the associated ConfigureDevice entity.
   */
  @belongsTo(
    () => ConfigureDevice,
    {keyTo: 'id'},
    {
      name: 'configure_devices_id',
    },
  )
  configureDeviceId: string;

  /**
   * Foreign key referencing the associated PlanSizes entity.
   */
  @belongsTo(
    () => PlanSizes,
    {keyTo: 'id'},
    {
      name: 'plan_size_id',
    },
  )
  planSizeId: string;

  /**
   * Has many PlanHistory records linked to this Plan.
   */
  @hasMany(() => PlanHistory, {
    keyTo: 'planId',
  })
  planHistories: PlanHistory[];

  @hasOne(() => BillingCycle)
  billingCycle: BillingCycle;

  @hasOne(() => Currency)
  currency: Currency;

  @hasOne(() => ConfigureDevice)
  configureDevice: ConfigureDevice;

  /**
   * Creates a new instance of Plan.
   * @param data - Partial data to initialize the model.
   */
  constructor(data?: Partial<Plan>) {
    super(data);
  }
}

/**
 * Interface describing relations of Plan (currently empty).
 */
export interface PlanRelations {}

/**
 * Type combining Plan with its relations.
 */
export type PlanWithRelations = Plan;
