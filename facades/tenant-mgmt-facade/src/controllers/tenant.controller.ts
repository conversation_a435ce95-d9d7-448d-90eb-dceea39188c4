import {authorize} from 'loopback4-authorization';
import {STRATEGY, authenticate} from 'loopback4-authentication';
import {
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/openapi-v3';
import {inject, service} from '@loopback/core';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {
  Tenant,
  CreateTenantDTO,
  VerifyKeyDto,
  KeySuggestionDto,
  EditTenantDTO,
  DashboardMetricsDto,
  TriggerDto,
} from '../models';
import {TenantHelperService, ApplicationWebhookService} from '../services';
import {RestBindings, Request} from '@loopback/rest';
import {PermissionKey, StatusDto} from '@local/core';
import {multipartRequestBody} from '@sourceloop/file-utils';
import {
  AnyObject,
  Count,
  CountSchema,
  Filter,
  Where,
} from '@loopback/repository';
import {DashboardHelperService} from '../services/dashboard-helper.service';

const basePath = '/tenants'; /**
 * Controller responsible for handling tenant-related operations,
 * including onboarding new tenants and verifying tenant keys.
 */
export class TenantController {
  /**
   * Creates a new instance of TenantController.
   * @param tenantHelper - The service responsible for tenant operations.
   * @param request - The HTTP request object injected by LoopBack.
   */
  constructor(
    @service(TenantHelperService)
    private readonly tenantHelper: TenantHelperService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @service(DashboardHelperService)
    private readonly dashboardHelper: DashboardHelperService,
    @service(ApplicationWebhookService)
    private readonly applicationWebhookService: ApplicationWebhookService,
  ) {}
  /**
   * Updates an existing tenant by ID.
   *
   * This endpoint:
   * - Updates tenant details and associated contact information.
   * - Requires `UpdateTenant` permission and Bearer token authentication.
   * - Triggers subscription management:
   *   - Subscription starts at the time of tenant creation.
   *   - Subscription end date is determined by the plan’s duration.
   *
   * @param {string} id - The unique identifier of the tenant to update.
   * @param {EditTenantDTO} dto - The tenant update payload, including contact info and optional file metadata.
   * @returns {Promise<void>} A promise that resolves once the tenant is successfully updated.
   *
   * @throws {HttpErrors.BadRequest} If the payload is invalid (e.g., bad JSON format in files or contact).
   * @throws {HttpErrors.NotFound} If the tenant does not exist.
   * @throws {HttpErrors.Forbidden} If the user lacks the required permissions.
   * @throws {HttpErrors.Unauthorized} If authentication fails.
   */
  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${basePath}/{id}/edit`, {
    description:
      'This api update a tenant with a contact info, address info, and plan and file info.',
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Tenant model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Tenant)},
        },
      },
    },
  })
  async updateTenant(
    @param.path.string('id') id: string,
    @multipartRequestBody(EditTenantDTO)
    dto: EditTenantDTO,
  ): Promise<Tenant> {
    return this.tenantHelper.editTenant(dto, id);
  }

  /**
   * Onboards a new tenant along with contact information.
   *
   * @remarks
   * This endpoint creates a new tenant. It also sets the start of the subscription
   * to the current date and calculates the end date based on the selected plan's duration.
   *
   * Requires `CreateTenant` permission.
   *
   * @param dto - The data transfer object containing tenant and contact details.
   * @returns The created Tenant instance.
   *
   * @response 200 - Successfully created tenant.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.CreateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    description:
      'This api creates a tenant with a contact, so it also expects contact info in the payload. The start of subscription is the time of creation of tenant and end date of plan depends on the duration of plan.',
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Tenant model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Tenant)},
        },
      },
    },
  })
  async onboard(
    @multipartRequestBody(CreateTenantDTO)
    dto: CreateTenantDTO,
  ): Promise<Tenant> {
    return this.tenantHelper.createTenant(dto);
  }

  /**
   * Retrieves the count of tenants based on the provided filter.
   *
   * @remarks
   * This endpoint fetches the count of tenants that match the specified filter criteria.
   * It returns the count as a number.
   *
   * Requires `ViewTenant` permission and bearer token authentication.
   *
   * @param where - The filter criteria for querying tenants.
   * @returns The count of tenants matching the filter.
   *
   * @response 200 - The count of tenants matching the filter.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Tenants Count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Tenant) where?: Where<Tenant>): Promise<Count> {
    return this.tenantHelper.countTenants(where);
  }

  /**
   * Verifies whether a given tenant key is available or provides suggestions if not.
   *
   * @remarks
   * This endpoint checks if a requested tenant key is already taken. If it is,
   * it responds with suggested alternative keys.
   *
   * Requires `ViewTenant` permission.
   *
   * @param dto - The DTO containing the key to be verified.
   * @returns Suggestions for alternative keys or confirmation of availability.
   *
   * @response 200 - Key verification result.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/verify-key`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description:
          'couples of possible leads if not requested key is not available',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(KeySuggestionDto)},
        },
      },
    },
  })
  async verifyKey(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(VerifyKeyDto),
        },
      },
    })
    dto: VerifyKeyDto,
  ): Promise<KeySuggestionDto> {
    return this.tenantHelper.verifyKey(dto);
  }

  /**
   * Retrieves all tenant statuses.
   *
   * @remarks
   * This endpoint fetches the current statuses of all tenants.
   *
   * Requires `ViewTenant` permission.
   *
   * @returns An array of tenant statuses.
   *
   * @response 200 - Successfully retrieved tenant statuses.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {passReqToCallback: true})
  @get(`${basePath}/all-statuses`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of tenant statuses',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(StatusDto, {partial: true}),
            },
          },
        },
      },
    },
  })
  async getAllStatuses(): Promise<StatusDto[]> {
    return this.tenantHelper.getAllStatuses();
  }

  /**
   * Retrieves all tenants.
   *
   * @remarks
   * This endpoint fetches the current tenants.
   *
   * Requires `ViewTenant` permission.
   *
   * @returns An array of tenants.
   *
   * @response 200 - Successfully retrieved tenants.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Tenant model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Tenant, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async getTenants(
    @param.filter(Tenant) filter?: Filter<Tenant>,
  ): Promise<Tenant[]> {
    return this.tenantHelper.getTenants(filter);
  }

  /**
   * Retrieves tenant details by tenant ID.
   *
   * @remarks
   * This endpoint fetches a specific tenant by its ID and returns the tenant details,
   * contact information, uploaded files, address details, and plan details.
   *
   * Requires `ViewTenant` permission and bearer token authentication.
   *
   * @param id - The ID of the tenant to retrieve details for.
   * @returns An object containing tenant details, contact info, files, address, and plan.
   *
   * @response 200 - Tenant details with contact, files, address, and plan information.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description:
          'Tenant details with contact, files, address, and plan information',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                tenantDetails: getModelSchemaRef(Tenant, {
                  includeRelations: true,
                }),
                contact: {
                  type: 'object',
                  description: 'Tenant admin contact details',
                },
                files: {
                  type: 'array',
                  description:
                    'Files uploaded for the tenant with pre-signed URLs',
                  items: {
                    type: 'object',
                    properties: {
                      id: {type: 'string'},
                      fileKey: {type: 'string'},
                      originalName: {type: 'string'},
                      source: {type: 'number'},
                      size: {type: 'number'},
                      signedUrl: {
                        type: 'string',
                        description: 'Pre-signed URL for file access',
                      },
                      tenantId: {type: 'string'},
                      createdOn: {type: 'string'},
                      modifiedOn: {type: 'string'},
                    },
                  },
                },
                address: {
                  type: 'object',
                  description: 'Address details of the tenant',
                },
                plan: {
                  type: 'object',
                  description: 'Plan details (currently empty)',
                },
              },
            },
          },
        },
      },
    },
  })
  async getTenantDetails(@param.path.string('id') id: string): Promise<{
    tenantDetails: object;
    contact: object;
    files: object[];
    address: object;
    plan: object;
  }> {
    return this.tenantHelper.getTenantDetails(id);
  }

  /**
   * Retrieves dashboard metrics for tenants.
   *
   * @remarks
   * This endpoint fetches dashboard metrics for tenants, including total tenants,
   * total active tenants, total inactive tenants, and total pending tenants.
   *
   * Requires `DashboardMetrics` permission and bearer token authentication.
   *
   * @param filter - Optional filter to query Tenant entities.
   * @returns An object containing dashboard metrics.
   *
   * @response 200 - Dashboard metrics.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.DashboardMetrics],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/dashboard-metrics`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      permissions: [PermissionKey.DashboardMetrics],
      [STATUS_CODE.OK]: {
        description: 'Dashboard metrics',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                totalTenants: {type: 'number'},
                totalActiveTenants: {type: 'number'},
                totalInactiveTenants: {type: 'number'},
                totalPendingTenants: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })
  async getDashboardMetrics(
    @param.filter(Tenant) filter?: Filter<Tenant>,
  ): Promise<DashboardMetricsDto> {
    return this.dashboardHelper.getDashboardMetrics(filter);
  }

  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/{id}/re-trigger`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'TriggerPipeline success',
      },
    },
  })
  async retriggerPipeline(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(TriggerDto, {
            title: 'TriggerDto',
          }),
        },
      },
    })
    triggerDto: TriggerDto,
    @param.path.string('id') id: string,
  ): Promise<void> {
    await this.tenantHelper.reTriggerPipeline(id, triggerDto);
  }

  /**
   * Triggers the provisioning process for a tenant.
   *
   * @remarks
   * This endpoint initiates the provisioning process for a specific tenant.
   *
   * Requires `ProvisionTenant` permission and bearer token authentication.
   *
   * @param id - The ID of the tenant to provision.
   * @returns A void promise indicating completion.
   *
   * @response 204 - Provisioning triggered successfully.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/{id}/provision`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'provision success',
      },
    },
  })
  async triggerProvision(@param.path.string('id') id: string): Promise<void> {
    await this.tenantHelper.triggerProvision(id);
  }

  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/{id}/de-provision`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'de provision success',
      },
    },
  })
  async triggerDeProvision(@param.path.string('id') id: string): Promise<void> {
    await this.tenantHelper.triggerDeProvision(id);
  }

  /**
   * Reactivates a tenant by its ID.
   *
   * @remarks
   * This endpoint reactivates a specific tenant by its ID.
   *
   * Requires `UpdateTenant` permission and bearer token authentication.
   *
   * @param id - The ID of the tenant to reactivate.
   * @returns A void promise indicating completion.
   *
   * @response 204 - Tenant reactivated successfully.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/{id}/reactivate`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Re-Activation success',
      },
    },
  })
  async reActivateTenant(@param.path.string('id') id: string): Promise<void> {
    await this.tenantHelper.reActivateTenant(id);
  }

  /**
   * Sends an invite to a tenant by its ID.
   *
   * @remarks
   * This endpoint sends an invite to a specific tenant by its ID.
   *
   * Requires `UpdateTenant` permission and bearer token authentication.
   *
   * @param id - The ID of the tenant to send an invite to.
   * @returns A void promise indicating completion.
   *
   * @response 204 - Invite sent successfully.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(`${basePath}/{id}/send-invite`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'create new subscription and send invoice success',
      },
    },
  })
  async sendInvite(@param.path.string('id') id: string): Promise<void> {
    await this.tenantHelper.sendInvite(id);
  }

  @authorize({
    permissions: [PermissionKey.ViewTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath + '/export', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Tenant model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Tenant, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async getTenantsCsv(): Promise<AnyObject> {
    return this.tenantHelper.getTenantCsvFile();
  }

  /**
   * Generates a token for a tenant using its ID and secret.
   *
   * @remarks
   * This endpoint generates a token for a specific tenant using its ID and secret.
   *
   * Requires `UpdateTenant` permission and bearer token authentication.
   *
   * @param tenantId - The ID of the tenant.
   * @param tenantSecret - The secret of the tenant.
   * @returns A string representing the generated token.
   *
   * @response 200 - Token generated successfully.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/token`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Token generated successfully',
        content: {
          [CONTENT_TYPE.TEXT]: {
            schema: {
              type: 'string',
            },
          },
        },
      },
    },
  })
  async generateToken(
    @param.query.string('tenantId') tenantId: string,
    @param.query.string('tenantSecret') tenantSecret: string,
  ): Promise<string> {
    return this.tenantHelper.returnToken(tenantId, tenantSecret);
  }

  /**
   * Validates a user within a specific tenant context.
   *
   * @remarks
   * This endpoint validates a user within a specific tenant context.
   *
   * Requires `UpdateTenant` permission and bearer token authentication.
   *
   * @param email - The email address of the user to validate.
   * @param userName - The username of the user to validate.
   * @param tenantId - The unique identifier of the tenant.
   * @returns A void promise indicating completion.
   *
   * @response 200 - User validated successfully.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.UpdateTenant],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/validate`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Token returned successfully',
      },
    },
  })
  async validateUser(
    @param.query.string('email') email: string,
    @param.query.string('userName') userName: string,
    @param.query.string('tenantId') tenantId: string,
  ): Promise<object> {
    return this.applicationWebhookService.validateUser(
      email,
      userName,
      tenantId,
    );
  }
}
