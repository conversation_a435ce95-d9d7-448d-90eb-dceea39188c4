import {
  Notification,
  NotificationEventType,
  NotificationType,
  PermissionKey,
  StatusDto,
  TemplateService,
  SubscriptionStatus,
  getCCEmailsForEvent,
} from '@local/core';
import {inject} from '@loopback/context';
import {service} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  Request,
  requestBody,
  RestBindings,
} from '@loopback/rest';
import {
  CONTENT_TYPE,
  OPERATION_SECURITY_SPEC,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {Plan} from '../models';
import {EditPlanDto} from '../models/dto/edit-plan.dto.model';
import {PlanStatusDto} from '../models/dto/plan-status.dto.model';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from '../services/proxies';
import {IPlan} from '../types';
import {generateInitialVersion, validateDecimal} from '../utils';

const basePath = '/plans';
const BEARER_PREFIX_LENGTH = 7;

/**
 * Controller for managing Plan entities.
 *
 * Provides endpoints to create, retrieve, and count plans, as well as to fetch plan statuses.
 */
export class PlansController {
  /**
   * Creates an instance of PlansController.
   *
   * @param subscriptionProxyService - Service proxy to interact with subscription-related operations.
   * @param request - Incoming HTTP request object.
   */
  constructor(
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,

    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,

    @service(TemplateService)
    private readonly templateService: TemplateService,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {}

  /**
   * Finds a Plan instance by its ID.
   *
   * @param id - The unique identifier of the Plan.
   * @param filter - Optional filter excluding 'where' clause.
   * @returns The Plan instance if found.
   *
   * @authorization Requires permission: ViewPlan
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewPlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Plan, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Plan, {exclude: 'where'}) filter?: FilterExcludingWhere<Plan>,
  ): Promise<Plan> {
    return this.subscriptionProxyService.findPlanById(
      this.request.headers.authorization ?? '',
      id,
      filter,
    );
  }

  /**
   * Counts the number of Plan instances matching the given criteria.
   *
   * @param where - Optional criteria to filter which plans to count.
   * @returns Count of plans matching the criteria.
   *
   * @authorization Requires permission: ViewPlan
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewPlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/count`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Plan) where?: Where<Plan>): Promise<Count> {
    const filterString = JSON.stringify(where);
    return this.subscriptionProxyService.getPlansCount(
      this.request.headers.authorization ?? '',
      filterString,
    );
  }

  /**
   * Retrieves an array of Plan instances.
   *
   * @param filter - Optional filter to query Plan entities.
   * @returns Array of Plan instances.
   *
   * @authorization Requires permission: ViewPlan
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.ViewPlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Plan model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Plan, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(@param.filter(Plan) filter?: Filter<IPlan>): Promise<IPlan[]> {
    const filterString = JSON.stringify(filter);
    const token = this.request.headers.authorization ?? '';
    return this.subscriptionProxyService.getPlans(token, filterString);
  }

  /**
   * Creates a new Plan instance.
   *
   * @param plan - Plan data excluding id, status, planSizeId, and version.
   * @returns The created Plan instance.
   *
   * @throws HttpErrors.BadRequest when costPerUser is missing or invalid while allowedUnlimitedUsers is false.
   * @throws Error if environment variables DEFAULT_PLAN_SIZE_ID or PLAN_VERSION_PATTERN are not set.
   *
   * @authorization Requires permission: CreatePlan
   * @authentication Bearer token required
   */
  @authorize({
    permissions: [PermissionKey.CreatePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @post(basePath, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Plan model instance',
        content: {
          'application/json': {schema: getModelSchemaRef(Plan)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Plan, {
            title: 'NewPlan',
            exclude: ['id', 'status', 'version', 'planSizeId', 'productRefId'],
          }),
        },
      },
    })
    plan: Omit<
      Plan,
      'id' | 'status' | 'planSizeId' | 'version' | 'productRefId'
    >,
  ): Promise<IPlan> {
    if (plan.name.toLowerCase().includes('free trial')) {
      throw new HttpErrors.BadRequest(
        'name can not include "Free Trial" Keyword',
      );
    }
    if (
      !plan.allowedUnlimitedUsers &&
      (plan.costPerUser == null || isNaN(plan.costPerUser))
    ) {
      throw new HttpErrors.BadRequest(
        'costPerUser is required when allowedUnlimitedUsers is false.',
      );
    }

    validateDecimal(plan.price, 'Price');

    if (!plan.allowedUnlimitedUsers) {
      validateDecimal(plan.costPerUser, 'CostPerUser');
    }

    const token = this.request.headers.authorization ?? '';
    if (!process.env.DEFAULT_PLAN_SIZE_ID) {
      throw new Error('DEFAULT_PLAN_SIZE_ID environment variable is not set');
    }
    if (!process.env.PLAN_VERSION_PATTERN) {
      throw new Error('PLAN_VERSION_PATTERN environment variable is not set');
    }
    const version = generateInitialVersion(process.env.PLAN_VERSION_PATTERN);
    return this.subscriptionProxyService.createPlan(token, {
      ...plan,
      planSizeId: process.env.DEFAULT_PLAN_SIZE_ID,
      version,
    });
  }

  /**
   * Retrieves all plan statuses.
   *
   * @remarks
   * This endpoint fetches the current plan statuses.
   *
   * Requires `ViewAllStatuses` permission.
   *
   * @returns An object containing all possible plan statuses.
   *
   * @response 200 - Successfully retrieved plan statuses.
   *
   * @security BearerToken
   */
  @authorize({
    permissions: [PermissionKey.ViewAllStatuses],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @get(`${basePath}/all-status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Object of all possible tenant status',
        permissions: [PermissionKey.ViewAllStatuses],
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              items: getModelSchemaRef(StatusDto, {
                title: 'PlanStatusDto',
              }),
            },
          },
        },
      },
    },
  })
  async findAllStatus(): Promise<StatusDto> {
    return this.subscriptionProxyService.getAllPlanStatus(
      this.request.headers.authorization ?? '',
    );
  }

  @authorize({
    permissions: [PermissionKey.UpdatePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${basePath}/{id}`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Plan PATCH success',
      },
    },
  })
  async updatePlanById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EditPlanDto, {partial: true}),
        },
      },
    })
    dto: EditPlanDto,
  ): Promise<void> {
    if (
      !dto.allowedUnlimitedUsers &&
      (dto.costPerUser == null || isNaN(dto.costPerUser))
    ) {
      throw new HttpErrors.BadRequest(
        'costPerUser is required when allowedUnlimitedUsers is false.',
      );
    }

    validateDecimal(dto.price, 'Price');

    if (!dto.allowedUnlimitedUsers) {
      validateDecimal(dto.costPerUser, 'CostPerUser');
    }

    const plan = await this.subscriptionProxyService.findPlanById(
      this.request.headers.authorization ?? '',
      id,
      {
        include: [
          {
            relation: 'currency',
          },
          {
            relation: 'billingCycle',
          },
        ],
      },
    );
    const planHistory = await this.subscriptionProxyService.getPlanHistory(
      this.request.headers.authorization ?? '',
      {
        where: {
          planId: id,
        },
      },
    );

    const count = planHistory.length + 2;
    if (!process.env.PLAN_VERSION_PATTERN) {
      throw new Error('PLAN_VERSION_PATTERN environment variable is not set');
    }
    const version = generateInitialVersion(
      process.env.PLAN_VERSION_PATTERN,
      count,
    );
    await this.subscriptionProxyService.createPlanHistory(
      this.request.headers.authorization ?? '',
      {
        price: Number(plan.price),
        version: plan.version,
        planId: id,
        allowedUnlimitedUsers: plan.allowedUnlimitedUsers,
        costPerUser: Number(plan.costPerUser),
      },
    );
    if (dto.allowedUnlimitedUsers) {
      dto.costPerUser = 0;
    }

    await this.subscriptionProxyService.updatePlanById(
      this.request.headers.authorization ?? '',
      id,
      {
        price: Number(dto.price),
        allowedUnlimitedUsers: dto.allowedUnlimitedUsers ?? false,
        costPerUser: Number(dto.costPerUser),
        version,
      },
    );

    const tenants = await this.tenantMgmtProxyService.getTenant(
      this.request.headers.authorization ?? '',
      {
        where: {
          planId: plan.id,
        },
        include: [
          {
            relation: 'contacts',
          },
        ],
      },
    );
    if (tenants.length === 0) return;

    // 2. Build filter for subscriptions
    const filter = JSON.stringify({
      where: {
        status: {inq: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PENDING]},
        planId: id,
      },
      include: [
        {
          relation: 'plan',
          scope: {
            include: [
              {relation: 'currency'},
              {relation: 'billingCycle'},
              {relation: 'configureDevice'},
            ],
          },
        },
      ],
    });

    // 3. Fetch subscriptions
    const subscriptions = await this.subscriptionProxyService.getSubscriptions(
      this.request.headers.authorization ?? '',
      filter,
    );

    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.PlanUpdate,
      NotificationType.EMAIL,
      this.request.headers.authorization?.substring(BEARER_PREFIX_LENGTH) ?? '',
    );

    if (!template) {
      throw new HttpErrors.InternalServerError(
        'Notification template for forget password not found',
      );
    }
    // 1. Build subscription lookup for O(1) access
    const subscriptionMap = new Map(
      subscriptions.map(sub => [sub.subscriberId, sub]),
    );

    // 2. Precompute common dto values once
    const allowUnlimitedUsers = dto.allowedUnlimitedUsers ? 1 : 0;
    const costPerUser = Number(dto.costPerUser ?? plan.costPerUser ?? 0);
    const planPrice = dto.price ?? plan.price;

    // 3. Generate notifications in one pass
    const notifications = tenants
      .map(tenant => {
        const subscription = subscriptionMap.get(tenant.id);

        if (!subscription) {
          return null; // skip tenants without subscription
        }

        const date = new Date(subscription.endDate ?? '');

        const formatter = new Intl.DateTimeFormat('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        });

        // Insert comma manually after month
        const dueDate = formatter
          .format(date)
          .replace(' ', ' ')
          .replace(' ', ', ');

        date.setDate(date.getDate() + 1);
        const effectiveDate = formatter
          .format(date)
          .replace(' ', ' ')
          .replace(' ', ', ');

        const numberOfUsers = subscription.numberOfUsers ?? 1;
        const totalCost =
          planPrice + costPerUser * (allowUnlimitedUsers ? 0 : numberOfUsers);
        const emailBody = this.templateService.generateEmail(template.body, {
          DISTEK_LOGO: process.env.DISTEK_LOGO ?? '',
          USER_NAME:
            tenant.contacts[0].firstName.trim() +
            ' ' +
            tenant.contacts[0].lastName.trim(),

          PLAN_NAME: plan.name ?? '',
          EFFECTIVE_DATE: effectiveDate,
          planName: plan.name ?? '',
          planPrice,
          allowUnlimitedUsers: dto.allowedUnlimitedUsers ? 'Yes' : 'No',
          costPerUser,
          totalCost,
          DUE_DATE: dueDate,
          SUPPORT_EMAIL: process.env.SUPPORT_EMAIL ?? '',
          HREF_SUPPORT: `mailto:${process.env.SUPPORT_EMAIL}`,
          BEST_REGARDS_BY: process.env.BEST_REGARDS_BY ?? 'Distek Team',
        });

        const ccEmails = getCCEmailsForEvent(template.eventName);

        return new Notification({
          subject: template.subject,
          body: emailBody,
          receiver: {
            to: [
              {
                id: tenant.id,
                toEmail: tenant.contacts?.[0]?.email ?? '',
                cc: ccEmails,
              },
            ],
          },
          type: 1,
          sentDate: new Date(),
          options: {
            fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
          },
        });
      })
      .filter((n): n is Notification => !!n); // remove nulls

    await this.notificationProxyService.createBulkNotification(
      this.request.headers.authorization?.substring(BEARER_PREFIX_LENGTH) ?? '',
      notifications,
    );
  }

  @authorize({
    permissions: [PermissionKey.UpdatePlan],
  })
  @authenticate(STRATEGY.BEARER, {
    passReqToCallback: true,
  })
  @patch(`${basePath}/{id}/status`, {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Plan PATCH success',
      },
    },
  })
  async updatePlanStatusById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PlanStatusDto, {partial: true}),
        },
      },
    })
    planStatus: PlanStatusDto,
  ): Promise<void> {
    await this.subscriptionProxyService.updatePlanById(
      this.request.headers.authorization ?? '',
      id,
      planStatus,
    );
  }
}
