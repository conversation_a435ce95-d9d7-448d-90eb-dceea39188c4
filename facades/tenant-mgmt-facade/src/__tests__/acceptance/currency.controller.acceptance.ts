import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {Currency} from '../../models';

describe('CurrencyController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      updateSubscription: sinon.stub(),
      getSoftwareLevel: sinon.stub(),
      createInvoice: sinon.stub(),
      resumeSubscriptionById: sinon.stub(),
      findPlanById: sinon.stub(),
      createCustomer: sinon.stub(),
      suspendSubscriptionById: sinon.stub(),
      renewSubscriptionInvite: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      cancelSubscriptionById: sinon.stub(),
      handleWebhook: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      createPrice: sinon.stub(), // Added stub for createPrice
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getSubscriptions: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
      updateConfigureDevices: sinon.stub(),
      findAllBillingStatusMetrics: sinon.stub(),
      getLastUnpaidInvoicePaymentLink: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns list of currencies when authorized', async () => {
    const token = getToken([PermissionKey.ViewCurrency]);

    const mockCurrencies: Currency[] = [
      new Currency({currencyCode: 'USD', currencyName: 'US Dollar'}),
      new Currency({currencyCode: 'EUR', currencyName: 'Euro'}),
    ];
    subscriptionServiceProxyServiceStub.getCurrencies.resolves(mockCurrencies);

    const res = await client
      .get('/currencies')
      .set('Authorization', token)
      .expect(200);

    // Convert model instances to plain objects for comparison
    expect(res.body).to.deepEqual(
      mockCurrencies.map(c =>
        typeof c.toJSON === 'function' ? c.toJSON() : {...c},
      ),
    );
  });

  it('returns empty array when no currencies found', async () => {
    subscriptionServiceProxyServiceStub.getCurrencies.resolves([]);

    const token = getToken([PermissionKey.ViewCurrency]);

    const res = await client
      .get('/currencies')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([]);
  });

  it('fails with 401 when no token is provided', async () => {
    await client.get('/currencies').expect(401);
    sinon.assert.notCalled(subscriptionServiceProxyServiceStub.getCurrencies);
  });
});
