import {
  NotificationTemplates,
  PermissionKey,
  PlanStatus,
  PlanTierType,
  StatusDto,
  SubscriptionStatus,
} from '@local/core';
import {Client, expect, sinon} from '@loopback/testlab';
import {TenantMgmtFacadeApplication} from '../../application';
import {Plan, PlanHistory, Subscription, Tenant} from '../../models';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from '../../services/proxies';
import {IPlan} from '../../types';
import * as Utils from '../../utils'; // add this near the top with other imports
import {getToken, setupApplication} from './test-helper';

describe('PlansController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;
  let tenantMgmtProxyStub: sinon.SinonStubbedInstance<TenantMgmtProxyService>;
  let notificationServiceProxyStub: sinon.SinonStubbedInstance<NotificationProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    notificationServiceProxyStub = {
      getTemplateByName: sinon.stub(),
      createNotification: sinon.stub(),
      createBulkNotification: sinon.stub(),
    };
    tenantMgmtProxyStub = {
      createTenant: sinon.stub(),
      verifyKey: sinon.stub(),
      getTenantById: sinon.stub(),
      getTenant: sinon.stub(),
      getTenantCount: sinon.stub(),
      getAllTenantStatus: sinon.stub(),
      updateTenant: sinon.stub(),
      triggerPipeline: sinon.stub(),
      provisionTenant: sinon.stub(),
      updateContactById: sinon.stub(),
      updateTenantById: sinon.stub(),
      getLeads: sinon.stub(),
      createLead: sinon.stub(),
      getLeadsCount: sinon.stub(),
      updateLeadById: sinon.stub(),
      getLeadById: sinon.stub(),
      getLeadStatuses: sinon.stub(),
      findAllStatusMetrics: sinon.stub(),
      handleWebhookTenantStatus: sinon.stub(),
    };
    subscriptionServiceProxyStub = {
      updateSubscription: sinon.stub(),
      findPlanById: sinon.stub(),
      createInvoice: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getSoftwareLevel: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createCustomer: sinon.stub(),
      resumeSubscriptionById: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      cancelSubscriptionById: sinon.stub(),
      renewSubscriptionInvite: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      // unused stubs but needed for full binding
      suspendSubscriptionById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      handleWebhook: sinon.stub(),
      createPrice: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getCurrencies: sinon.stub(),
      getSubscriptions: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
      updateConfigureDevices: sinon.stub(),
      findAllBillingStatusMetrics: sinon.stub(),
      getLastUnpaidInvoicePaymentLink: sinon.stub(),
    };
    app
      .bind('services.NotificationProxyService')
      .to(notificationServiceProxyStub);
    app.bind(`services.TenantMgmtProxyService`).to(tenantMgmtProxyStub);

    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyStub);
  });

  afterEach(() => sinon.restore());

  it('retrieves a plan by ID when authorized', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const mockPlan = new Plan({
      id: 'plan-123',
      name: 'Pro Plan',
      tier: PlanTierType.PREMIUM,
      price: 50,
      status: PlanStatus.ACTIVE,
      version: 'v1.0.0',
      allowedUnlimitedUsers: false,
      costPerUser: 10,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
      planSizeId: 'size-1',
    });

    subscriptionServiceProxyStub.findPlanById.resolves(mockPlan);

    const res = await client
      .get('/plans/plan-123')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual(mockPlan.toJSON());
  });

  it('retrieves plan count', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const where = {status: PlanStatus.ACTIVE};

    subscriptionServiceProxyStub.getPlansCount.resolves({count: 5});

    const res = await client
      .get('/plans/count')
      .query({where})
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual({count: 5});
  });

  it('retrieves plans list', async () => {
    const token = getToken([PermissionKey.ViewPlan]);
    const mockPlans: IPlan[] = [
      {
        id: 'p1',
        name: 'Basic Plan',
        tier: PlanTierType.STANDARD,
        price: 20,
        status: PlanStatus.ACTIVE,
        version: 'v1',
        allowedUnlimitedUsers: true,
        costPerUser: 0,
        billingCycleId: 'cycle-1',
        currencyId: 'curr-1',
        configureDeviceId: 'config-1',
        planSizeId: 'size-1',
      },
    ];

    subscriptionServiceProxyStub.getPlans.resolves(mockPlans);

    await client.get('/plans').set('Authorization', token).expect(200);
  });

  it('creates a plan successfully', async () => {
    process.env.DEFAULT_PLAN_SIZE_ID = 'size-123';
    process.env.PLAN_VERSION_PATTERN = 'v1.0.0';

    const token = getToken([PermissionKey.CreatePlan]);
    const requestBody = {
      name: 'Enterprise Plan',
      tier: PlanTierType.PREMIUM,
      price: 100,
      allowedUnlimitedUsers: false,
      costPerUser: 5,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    const createdPlan = {
      ...requestBody,
      id: 'new-plan',
      version: 'v1.0.0',
    };

    subscriptionServiceProxyStub.createPlan.resolves(createdPlan);

    const res = await client
      .post('/plans')
      .set('Authorization', token)
      .send(requestBody)
      .expect(200);

    expect(res.body).to.containEql(createdPlan);
  });

  it('throws 400 when allowedUnlimitedUsers is false but costPerUser is missing', async () => {
    const token = getToken([PermissionKey.CreatePlan]);
    const invalidBody = {
      name: 'Bad Plan',
      tier: PlanTierType.PREMIUM,
      price: 10,
      allowedUnlimitedUsers: false,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    await client
      .post('/plans')
      .set('Authorization', token)
      .send(invalidBody)
      .expect(400);
  });

  it('retrieves all plan statuses', async () => {
    const token = getToken([PermissionKey.ViewAllStatuses]);
    const mockStatuses: StatusDto = new StatusDto({
      statuses: {
        0: 'ACTIVE',
        1: 'INACTIVE',
      },
    });

    subscriptionServiceProxyStub.getAllPlanStatus.resolves(mockStatuses);

    const res = await client
      .get('/plans/all-status')
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.deepEqual({statuses: {0: 'ACTIVE', 1: 'INACTIVE'}});
  });

  it('fails with 401 when no token provided', async () => {
    await client.get('/plans').expect(401);
    sinon.assert.notCalled(subscriptionServiceProxyStub.getPlans);
  });

  it('updates a plan by id (allowedUnlimitedUsers=true forces costPerUser=0) and sends notification', async () => {
    process.env.PLAN_VERSION_PATTERN = 'vX.Y.Z';
    process.env.NOTIFICATION_FROM_EMAIL = '<EMAIL>';

    const token = getToken([PermissionKey.UpdatePlan]);

    const existingPlan = new Plan({
      id: 'plan-123',
      name: 'Pro Plan',
      tier: PlanTierType.PREMIUM,
      price: 50,
      status: 0,
      version: 'v1.0.0',
      allowedUnlimitedUsers: false,
      costPerUser: 10,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
      planSizeId: 'size-1',
    });

    // history length = 1 -> count = 3 -> version should be whatever utils returns
    const history = [
      {id: 'h1', planId: 'plan-123', version: 'v0.9.0'},
    ] as unknown as PlanHistory[];

    // --- Stubs ---
    subscriptionServiceProxyStub.findPlanById.resolves(existingPlan);
    subscriptionServiceProxyStub.getPlanHistory.resolves(history);

    subscriptionServiceProxyStub.getSubscriptions.resolves([
      {
        id: 'sub-1',
        subscriberId: 'tenant-1',
        planId: 'plan-abc',
        status: SubscriptionStatus.ACTIVE,
        plan: {
          id: 'plan-abc',
          name: '',
          tier: PlanTierType.PREMIUM,
          price: 0,
          status: PlanStatus.ACTIVE,
          productRefId: '',
          version: '',
          allowedUnlimitedUsers: false,
          billingCycleId: '',
          currencyId: '',
          configureDeviceId: '',
          planSizeId: '',
          planHistories: [],
        } as unknown as Plan,
        externalSubscriptionId: 'subs-123',
        priceRefId: 'price-123',
        totalCost: 200,
        endDate: new Date().toISOString(),
      } as unknown as Subscription & {plan: Plan},
    ]);
    const versionStub = sinon
      .stub(Utils, 'generateInitialVersion')
      .returns('v1.0.2');

    tenantMgmtProxyStub.getTenant.resolves([
      {
        id: 'tenant-1',
        planName: 'Pro Plan',
        contacts: [
          {email: '<EMAIL>', firstName: 'John', lastName: 'Doe'},
        ],
      },
    ] as unknown as Tenant[]);

    notificationServiceProxyStub.getTemplateByName.resolves({
      subject: 'Plan updated',
      body: 'Plan {{planName}} updated',
      eventName: '',
      notificationType: 0,
    } as unknown as NotificationTemplates);

    notificationServiceProxyStub.createNotification.resolves();

    // --- Call API ---
    await client
      .patch('/plans/plan-123')
      .set('Authorization', token)
      .send({
        price: 150,
        allowedUnlimitedUsers: true, // triggers costPerUser -> 0
      })
      .expect(204);

    // --- Assertions ---

    // Plan fetch includes relations
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.findPlanById,
      sinon.match.string,
      'plan-123',
      {
        include: [{relation: 'currency'}, {relation: 'billingCycle'}],
      },
    );

    // previous plan snapshot saved
    sinon.assert.calledOnce(subscriptionServiceProxyStub.createPlanHistory);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.createPlanHistory,
      sinon.match.string, // auth token
      sinon.match({
        planId: 'plan-123',
        version: 'v1.0.0',
        price: 50,
        allowedUnlimitedUsers: false,
        costPerUser: 10,
      }),
    );

    // update applied with forced costPerUser=0 and computed version
    sinon.assert.calledOnce(subscriptionServiceProxyStub.updatePlanById);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.updatePlanById,
      sinon.match.string, // auth token
      'plan-123',
      {
        price: 150,
        allowedUnlimitedUsers: true,
        costPerUser: 0,
        version: 'v1.0.2',
      },
    );

    // tenant + notification flow
    sinon.assert.calledOnce(tenantMgmtProxyStub.getTenant);
    sinon.assert.calledOnce(notificationServiceProxyStub.getTemplateByName);
    sinon.assert.calledOnce(
      notificationServiceProxyStub.createBulkNotification,
    );

    versionStub.restore();
    delete process.env.PLAN_VERSION_PATTERN;
    delete process.env.NOTIFICATION_FROM_EMAIL;
  });

  it('returns 400 when updating plan with allowedUnlimitedUsers=false and missing costPerUser', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);

    await client
      .patch('/plans/plan-123')
      .set('Authorization', token)
      .send({
        allowedUnlimitedUsers: false,
        // costPerUser omitted -> 400
      })
      .expect(400);

    sinon.assert.notCalled(subscriptionServiceProxyStub.updatePlanById);
    sinon.assert.notCalled(subscriptionServiceProxyStub.createPlanHistory);
  });

  it('returns 500 when updating plan if PLAN_VERSION_PATTERN is not set', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);

    // Must still resolve these before it hits the env check
    subscriptionServiceProxyStub.findPlanById.resolves(
      new Plan({
        id: 'plan-123',
        name: 'Pro Plan',
        tier: PlanTierType.PREMIUM,
        price: 50,
        status: 0,
        version: 'v1.0.0',
        allowedUnlimitedUsers: false,
        costPerUser: 10,
        billingCycleId: 'cycle-1',
        currencyId: 'curr-1',
        configureDeviceId: 'config-1',
        planSizeId: 'size-1',
      }),
    );
    subscriptionServiceProxyStub.getPlanHistory.resolves([]);

    // Ensure not set
    delete process.env.PLAN_VERSION_PATTERN;

    await client
      .patch('/plans/plan-123')
      .set('Authorization', token)
      .send({
        price: 60,
        allowedUnlimitedUsers: false,
        costPerUser: 12,
      })
      .expect(500);

    // No writes should happen
    sinon.assert.notCalled(subscriptionServiceProxyStub.createPlanHistory);
    sinon.assert.notCalled(subscriptionServiceProxyStub.updatePlanById);
  });

  it('updates plan status by id', async () => {
    const token = getToken([PermissionKey.UpdatePlan]);

    await client
      .patch('/plans/plan-123/status')
      .set('Authorization', token)
      .send({status: 1}) // e.g., PlanStatus.INACTIVE
      .expect(204);

    sinon.assert.calledOnce(subscriptionServiceProxyStub.updatePlanById);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.updatePlanById,
      sinon.match.string, // auth
      'plan-123',
      {status: 1},
    );
  });

  it('returns 500 when creating a plan if DEFAULT_PLAN_SIZE_ID is not set', async () => {
    process.env.PLAN_VERSION_PATTERN = 'v1.0.0';
    delete process.env.DEFAULT_PLAN_SIZE_ID;

    const token = getToken([PermissionKey.CreatePlan]);
    const requestBody = {
      name: 'Enterprise Plan',
      tier: PlanTierType.PREMIUM,
      price: 100,
      allowedUnlimitedUsers: false,
      costPerUser: 5,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    await client
      .post('/plans')
      .set('Authorization', token)
      .send(requestBody)
      .expect(500);

    delete process.env.PLAN_VERSION_PATTERN;
  });

  it('returns 500 when creating a plan if PLAN_VERSION_PATTERN is not set', async () => {
    process.env.DEFAULT_PLAN_SIZE_ID = 'size-123';
    delete process.env.PLAN_VERSION_PATTERN;

    const token = getToken([PermissionKey.CreatePlan]);
    const requestBody = {
      name: 'Enterprise Plan',
      tier: PlanTierType.PREMIUM,
      price: 100,
      allowedUnlimitedUsers: false,
      costPerUser: 5,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
    };

    await client
      .post('/plans')
      .set('Authorization', token)
      .send(requestBody)
      .expect(500);

    delete process.env.DEFAULT_PLAN_SIZE_ID;
  });

  it('updates a plan by id with allowedUnlimitedUsers=false and valid costPerUser and sends notification', async () => {
    process.env.PLAN_VERSION_PATTERN = 'vX.Y.Z';
    process.env.NOTIFICATION_FROM_EMAIL = '<EMAIL>';

    const token = getToken([PermissionKey.UpdatePlan]);

    const existingPlan = new Plan({
      id: 'plan-abc',
      name: 'Standard Plan',
      tier: PlanTierType.STANDARD,
      price: 25,
      status: 0,
      version: 'v1.0.1',
      allowedUnlimitedUsers: true,
      costPerUser: 0,
      billingCycleId: 'cycle-1',
      currencyId: 'curr-1',
      configureDeviceId: 'config-1',
      planSizeId: 'size-1',
    });

    // --- Stubs ---
    subscriptionServiceProxyStub.findPlanById.resolves(existingPlan);
    subscriptionServiceProxyStub.getPlanHistory.resolves([
      {id: 'h1'},
    ] as unknown as PlanHistory[]);
    subscriptionServiceProxyStub.getSubscriptions.resolves([
      {
        id: 'sub-1',
        subscriberId: 'tenant-1',
        planId: 'plan-abc',
        status: SubscriptionStatus.ACTIVE,
        plan: {
          id: 'plan-abc',
          name: '',
          tier: PlanTierType.PREMIUM,
          price: 0,
          status: PlanStatus.ACTIVE,
          productRefId: '',
          version: '',
          allowedUnlimitedUsers: false,
          billingCycleId: '',
          currencyId: '',
          configureDeviceId: '',
          planSizeId: '',
          planHistories: [],
        } as unknown as Plan,
        externalSubscriptionId: 'subs-123',
        priceRefId: 'price-123',
        totalCost: 200,
        endDate: new Date().toISOString(),
      } as unknown as Subscription & {plan: Plan},
    ]);

    const versionStub = sinon
      .stub(Utils, 'generateInitialVersion')
      .returns('v1.0.3');

    tenantMgmtProxyStub.getTenant.resolves([
      {
        id: 'tenant-1',
        planName: 'Standard Plan',
        contacts: [
          {email: '<EMAIL>', firstName: 'John', lastName: 'Doe'},
        ],
      },
    ] as unknown as Tenant[]);

    notificationServiceProxyStub.getTemplateByName.resolves({
      subject: 'Plan updated',
      body: 'Plan {{planName}} updated',
      eventName: '',
      notificationType: 0,
    } as unknown as NotificationTemplates);

    notificationServiceProxyStub.createNotification.resolves();

    // --- Call API ---
    await client
      .patch('/plans/plan-abc')
      .set('Authorization', token)
      .send({
        price: 30,
        allowedUnlimitedUsers: false,
        costPerUser: 3,
      })
      .expect(204);

    // --- Assertions ---
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.findPlanById,
      sinon.match.string,
      'plan-abc',
      {
        include: [{relation: 'currency'}, {relation: 'billingCycle'}],
      },
    );

    sinon.assert.calledOnce(subscriptionServiceProxyStub.createPlanHistory);
    sinon.assert.calledWithMatch(
      subscriptionServiceProxyStub.updatePlanById,
      sinon.match.string,
      'plan-abc',
      {
        price: 30,
        allowedUnlimitedUsers: false,
        costPerUser: 3,
        version: 'v1.0.3',
      },
    );

    sinon.assert.calledOnce(tenantMgmtProxyStub.getTenant);
    sinon.assert.calledOnce(notificationServiceProxyStub.getTemplateByName);
    sinon.assert.calledOnce(
      notificationServiceProxyStub.createBulkNotification,
    );

    versionStub.restore();
    delete process.env.PLAN_VERSION_PATTERN;
    delete process.env.NOTIFICATION_FROM_EMAIL;
  });
});
