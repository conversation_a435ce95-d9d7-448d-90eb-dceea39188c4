import {Client, expect} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import sinon from 'sinon';
import FormData from 'form-data';
import {MulterMemoryStorage} from '@sourceloop/file-utils';
import {TenantMgmtFacadeApplication} from '../../application';
import {
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from '../../services/proxies';
import {
  HTTP_STATUS,
  PermissionKey,
  PriceDto,
  StorageSource,
  TenantStatus,
  SubscriptionStatus,
} from '@local/core';
import {
  BufferFile,
  buildTenantDto,
  dummyPlan,
  tenantPayload,
} from './mock-data';
import {
  CustomerDto,
  KeySuggestionDto,
  Plan,
  Subscription,
  VerifyKeyDto,
} from '../../models';

const basePath = '/tenants';

describe('TenantController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let tenantMgmtProxyStub: sinon.SinonStubbedInstance<TenantMgmtProxyService>;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  beforeEach(async () => {
    tenantMgmtProxyStub = {
      createTenant: sinon.stub(),
      verifyKey: sinon.stub(),
      getTenantById: sinon.stub(),
      getTenant: sinon.stub(),
      getTenantCount: sinon.stub(),
      getAllTenantStatus: sinon.stub(),
      provisionTenant: sinon.stub(),
      updateContactById: sinon.stub(),
      updateTenantById: sinon.stub(),
      getLeads: sinon.stub(),
      createLead: sinon.stub(),
      getLeadsCount: sinon.stub(),
      updateLeadById: sinon.stub(),
      getLeadById: sinon.stub(),
      getLeadStatuses: sinon.stub(),
      findAllStatusMetrics: sinon.stub(),
      handleWebhookTenantStatus: sinon.stub(),
    };

    subscriptionServiceProxyServiceStub = {
      updateSubscription: sinon.stub(),
      createInvoice: sinon.stub(),
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      createPrice: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      handleWebhook: sinon.stub(),
      createCustomer: sinon.stub(),
      getSubscriptions: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(),
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
      updateConfigureDevices: sinon.stub(),
    };

    app.bind(`services.TenantMgmtProxyService`).to(tenantMgmtProxyStub);
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
    app.bind(`services.MulterS3Storage`).toProvider(MulterMemoryStorage);

    const fileAdapterServiceStub = {
      generateFileResponse: sinon.stub().resolves([
        {
          fileKey: 'test.pdf',
          originalName: 'dummy.pdf',
          source: StorageSource.S3,
          size: BufferFile.length,
        },
      ]),
      generateFileInfoWithSignedUrl: sinon.stub(),
      generateSignedUrl: sinon.stub(),
      generateLocalStackViewUrl: sinon.stub(),
    };

    app.bind('services.FileAdapterService').to(fileAdapterServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  after(async () => {
    await app.stop();
  });

  it('invokes PATCH /tenants/{id}/edit', async () => {
    const token = getToken([PermissionKey.UpdateTenant]);
    subscriptionServiceProxyServiceStub.getSubscriptions.resolves([
      {
        id: 'sub-123',
        externalSubscriptionId: 'ext-sub-123',
        priceRefId: 'price-123',
        plan: dummyPlan,
      },
    ] as unknown as (Subscription & {plan: Plan})[]);
    subscriptionServiceProxyServiceStub.findPlanById.resolves(
      new Plan({
        ...dummyPlan,
        planHistories: [],
      }),
    );
    subscriptionServiceProxyServiceStub.updateSubscriptionById.resolves();

    subscriptionServiceProxyServiceStub.createPrice.resolves({
      id: 'price-123',
    } as unknown as PriceDto);
    await client
      .patch(`${basePath}/tenant-123/edit`)
      .set('Authorization', `${token}`)
      .field('totalCost', 100)
      .field('planId', 'plan-1')
      .field(
        'existingFiles',
        JSON.stringify([
          {
            fileKey: 'test.pdf',
            originalName: 'dummy.pdf',
            source: StorageSource.S3,
            size: 123,
          },
        ]),
      )
      .expect(HTTP_STATUS.NO_CONTENT);
  });

  it('invokes POST /tenants', async () => {
    const token = getToken([PermissionKey.CreateTenant]);
    subscriptionServiceProxyServiceStub.findPlanById.resolves(
      new Plan({
        ...dummyPlan,
        planHistories: [],
      }),
    );
    tenantMgmtProxyStub.createTenant.resolves(buildTenantDto());
    subscriptionServiceProxyServiceStub.createSubscriptions.resolves({
      id: 'sub-123',
      subscriberId: '',
      externalSubscriptionId: '',
      priceRefId: '',
      totalCost: 0,
      status: SubscriptionStatus.PENDING,
      planId: '',
    } as unknown as Subscription);
    subscriptionServiceProxyServiceStub.createCustomer.resolves({
      id: 'cust-123',
    } as unknown as CustomerDto);
    subscriptionServiceProxyServiceStub.createPrice.resolves({
      id: 'price-123',
    } as unknown as PriceDto);
    tenantMgmtProxyStub.provisionTenant.resolves();
    const {body} = await client
      .post(basePath)
      .set('Authorization', `${token}`)
      .field('name', tenantPayload.name)
      .field('lang', tenantPayload.lang)
      .field('overallPlan', JSON.stringify(tenantPayload.overallPlan))
      .field(
        'contact',
        JSON.stringify({
          firstName: tenantPayload.contact.firstName,
          lastName: tenantPayload.contact.lastName,
          isPrimary: true,
          email: tenantPayload.contact.email,
          phoneNumber: tenantPayload.contact.phoneNumber,
          countryCode: tenantPayload.contact.countryCode,
          designation: tenantPayload.contact.designation,
        }),
      )
      .field('key', tenantPayload.key)
      .attach('files', BufferFile, {
        filename: 'dummy.pdf',
        contentType: 'application/pdf',
      })
      .expect(HTTP_STATUS.OK);

    expect(body.key).to.eql('keyTenant');
    expect(body.name).to.eql('Tenant');
  });
  it('responds with 401 Unauthorized for missing token', async () => {
    const formData = new FormData();
    const formStream = formData.getBuffer();
    await client
      .post('/tenants')
      .set(
        'Content-Type',
        `multipart/form-data; boundary=${formData.getBoundary()}`,
      )
      .send(formStream)
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  it('invokes POST /tenants/verify-key successfully', async () => {
    // Arrange
    const token = getToken([PermissionKey.ViewTenant]);
    const requestBody: VerifyKeyDto = new VerifyKeyDto({
      key: 'unique-tenant-key',
    });

    const responseMock: KeySuggestionDto = new KeySuggestionDto({
      available: false,
      suggestions: ['unique-tenant-key1', 'unique-tenant-key2'],
    });

    tenantMgmtProxyStub.verifyKey.resolves(responseMock);

    const {body} = await client
      .post(`${basePath}/verify-key`)
      .set('Authorization', `${token}`)
      .send(requestBody)
      .expect(HTTP_STATUS.OK);

    expect(body.available).to.be.false();
    expect(body.suggestions).to.containDeep(['unique-tenant-key1']);
  });

  it('responds with 401 Unauthorized for missing token for verify-key endpoint', async () => {
    await client
      .post(`${basePath}/verify-key`)
      .send({key: 'unique-tenant-key'})
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });
  // Test case for GET /tenants/count
  it('invokes GET /tenants/count successfully', async () => {
    const len = 5;
    const token = getToken([PermissionKey.ViewTenant]);
    tenantMgmtProxyStub.getTenantCount.resolves({count: 5});

    const {body} = await client
      .get(`${basePath}/count`)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(body.count).to.eql(len);
  });

  // Test case for GET /tenants
  it('invokes GET /tenants successfully', async () => {
    const token = getToken([PermissionKey.ViewTenant]);
    const tenantsMock = [
      {
        id: '1',
        name: 'Tenant1',
        status: TenantStatus.ACTIVE,
        key: 'key1',
        domains: ['tenant1.com'],
        contacts: [],
        resources: [],
        addressId: 'address1',
        lang: 'en',
        createdBy: 'user1',
        createdOn: new Date().toISOString(),
        files: [],
      },
      {
        id: '2',
        name: 'Tenant2',
        status: TenantStatus.INACTIVE,
        key: 'key2',
        domains: ['tenant2.com'],
        contacts: [],
        resources: [],
        addressId: 'address2',
        lang: 'fr',
        createdBy: 'user2',
        createdOn: new Date().toISOString(),
        files: [],
      },
    ];
    tenantMgmtProxyStub.getTenant.resolves([
      {
        id: '1',
        name: 'Tenant1',
        status: TenantStatus.ACTIVE,
        key: 'key1',
        domains: ['tenant1.com'],
        contacts: [],
        resources: [],
        addressId: 'address1',
        lang: 'en',
        createdBy: 'user1',
        createdOn: new Date(),
        files: [],
        getId: () => '1',
        getIdObject: () => ({id: '1'}),
        toJSON: function () {
          return {
            id: this.id,
            name: this.name,
            status: this.status,
            key: this.key,
            domains: this.domains,
            contacts: this.contacts,
            resources: this.resources,
            addressId: this.addressId,
            lang: this.lang,
            createdBy: this.createdBy,
            createdOn: this.createdOn?.toISOString(), // Optional: normalize
            files: this.files,
          };
        },
        toObject: () => ({}),
      },
      {
        id: '2',
        name: 'Tenant2',
        status: TenantStatus.INACTIVE,
        key: 'key2',
        domains: ['tenant2.com'],
        contacts: [],
        resources: [],
        addressId: 'address2',
        lang: 'fr',
        createdBy: 'user2',
        createdOn: new Date(),
        files: [],
        getId: () => '2',
        getIdObject: () => ({id: '2'}),
        toJSON: function () {
          return {
            id: this.id,
            name: this.name,
            status: this.status,
            key: this.key,
            domains: this.domains,
            contacts: this.contacts,
            resources: this.resources,
            addressId: this.addressId,
            lang: this.lang,
            createdBy: this.createdBy,
            createdOn: this.createdOn?.toISOString(), // Optional: normalize
            files: this.files,
          };
        },
        toObject: () => ({}),
      },
    ]);

    const {body} = await client
      .get(basePath)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    expect(body).to.containDeep(tenantsMock); //sunny kannnan
  });

  // Test case for GET /tenants/{id}
  it('invokes GET /tenants/{id} successfully', async () => {
    const token = getToken([PermissionKey.ViewTenant]);
    const tenantId = 'tenant-123';

    // Mock tenant details response (with required Tenant interface methods)
    const mockTenantDetailsResponse = {
      id: tenantId,
      name: 'Test Tenant',
      key: 'test-tenant-key',
      status: TenantStatus.ACTIVE,
      domains: ['test-tenant.com'],
      lang: 'en',
      resources: [],
      addressId: 'address-1',
      contacts: [
        {
          id: 'contact-1',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          isPrimary: true,
          tenantId: tenantId,
        },
      ],
      files: [
        {
          id: 'file-1',
          fileKey: '1754137174168',
          originalName: 'contract.pdf',
          source: StorageSource.S3,
          size: 582613,
          tenantId: tenantId,
          createdOn: new Date().toISOString(),
          modifiedOn: new Date().toISOString(),
        },
        {
          id: 'file-2',
          fileKey: '1754137174169',
          originalName: 'invoice.pdf',
          source: StorageSource.S3,
          size: 20700,
          tenantId: tenantId,
          createdOn: new Date().toISOString(),
          modifiedOn: new Date().toISOString(),
        },
      ],
      address: {
        id: 'address-1',
        street: '123 Main St',
        city: 'Test City',
        state: 'Test State',
        country: 'Test Country',
        zip: '12345',
      },
      createdBy: 'user1',
      createdOn: new Date().toISOString(),
      getId: () => tenantId,
      getIdObject: () => ({id: tenantId}),
      toJSON: function () {
        return {
          id: this.id,
          name: this.name,
          key: this.key,
          status: this.status,
          domains: this.domains,
          lang: this.lang,
          resources: this.resources,
          addressId: this.addressId,
          contacts: this.contacts,
          files: this.files,
          address: this.address,
          createdBy: this.createdBy,
          createdOn: this.createdOn,
        };
      },
      toObject: () => ({}),
    };

    // Mock the signed URLs for files
    const signedUrl1 = 'http://localhost:4566/bucket/contract.pdf?signed=true';
    const signedUrl2 = 'http://localhost:4566/bucket/invoice.pdf?signed=true';

    // Setup stubs for service responses
    (tenantMgmtProxyStub.getTenantById as sinon.SinonStub).resolves(
      mockTenantDetailsResponse,
    );

    // Mock file adapter service to return signed URLs
    const fileAdapterService = app.getSync('services.FileAdapterService');
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (fileAdapterService as any).generateLocalStackViewUrl
      .onFirstCall()
      .resolves(signedUrl1)
      .onSecondCall()
      .resolves(signedUrl2);

    const {body} = await client
      .get(`${basePath}/${tenantId}`)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    // Verify response structure
    expect(body).to.have.properties([
      'tenantDetails',
      'contact',
      'files',
      'address',
      'plan',
    ]);

    // Verify tenant details
    expect(body.tenantDetails).to.have.properties([
      'id',
      'name',
      'key',
      'status',
    ]);
    expect(body.tenantDetails.id).to.eql(tenantId);
    expect(body.tenantDetails.name).to.eql('Test Tenant');
    expect(body.tenantDetails.key).to.eql('test-tenant-key');

    // Verify contact (should be the first contact)
    expect(body.contact).to.have.properties([
      'id',
      'email',
      'firstName',
      'lastName',
    ]);
    expect(body.contact.email).to.eql('<EMAIL>');
    expect(body.contact.firstName).to.eql('Admin');
    expect(body.contact.isPrimary).to.eql(true);

    // Verify files with signed URLs
    expect(Array.isArray(body.files)).to.be.true();
    expect(body.files).to.have.length(2);

    expect(body.files[0]).to.have.properties([
      'id',
      'fileKey',
      'originalName',
      'signedUrl',
      'size',
      'source',
    ]);
    expect(body.files[0].signedUrl).to.eql(signedUrl1);
    expect(body.files[0].originalName).to.eql('contract.pdf');
    expect(body.files[0].fileKey).to.eql('1754137174168');

    expect(body.files[1]).to.have.properties([
      'id',
      'fileKey',
      'originalName',
      'signedUrl',
      'size',
      'source',
    ]);
    expect(body.files[1].signedUrl).to.eql(signedUrl2);
    expect(body.files[1].originalName).to.eql('invoice.pdf');
    expect(body.files[1].fileKey).to.eql('1754137174169');

    // Verify address
    expect(body.address).to.have.properties(['id', 'street', 'city', 'state']);
    expect(body.address.street).to.eql('123 Main St');
    expect(body.address.city).to.eql('Test City');

    // Verify plan is empty object
    expect(typeof body.plan).to.eql('object');
    expect(body.plan).to.be.empty();
  });

  // Test case for GET /tenants/{id} with missing authorization
  it('responds with 401 Unauthorized for missing token on GET /tenants/{id}', async () => {
    const tenantId = 'tenant-123';

    await client
      .get(`${basePath}/${tenantId}`)
      .expect(HTTP_STATUS.UNAUTHORIZED);
  });

  // Test case for GET /tenants/{id} with tenant that has no files
  it('invokes GET /tenants/{id} successfully for tenant with no files', async () => {
    const token = getToken([PermissionKey.ViewTenant]);
    const tenantId = 'tenant-no-files';

    // Mock tenant response with no files
    const mockTenantResponse = {
      id: tenantId,
      name: 'Tenant Without Files',
      key: 'tenant-no-files-key',
      status: TenantStatus.ACTIVE,
      domains: ['no-files.com'],
      lang: 'en',
      resources: [],
      addressId: 'address-1',
      contacts: [
        {
          id: 'contact-1',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          isPrimary: true,
          tenantId: tenantId,
        },
      ],
      files: [], // No files
      address: {
        id: 'address-1',
        street: '456 No Files St',
        city: 'Empty City',
        state: 'Empty State',
        country: 'Test Country',
        zip: '54321',
      },
      createdBy: 'user1',
      createdOn: new Date().toISOString(),
      getId: () => tenantId,
      getIdObject: () => ({id: tenantId}),
      toJSON: function () {
        return {
          id: this.id,
          name: this.name,
          key: this.key,
          status: this.status,
          domains: this.domains,
          lang: this.lang,
          resources: this.resources,
          addressId: this.addressId,
          contacts: this.contacts,
          files: this.files,
          address: this.address,
          createdBy: this.createdBy,
          createdOn: this.createdOn,
        };
      },
      toObject: () => ({}),
    };

    (tenantMgmtProxyStub.getTenantById as sinon.SinonStub).resolves(
      mockTenantResponse,
    );

    const {body} = await client
      .get(`${basePath}/${tenantId}`)
      .set('Authorization', `${token}`)
      .expect(HTTP_STATUS.OK);

    // Verify response structure
    expect(body).to.have.properties([
      'tenantDetails',
      'contact',
      'files',
      'address',
      'plan',
    ]);

    // Verify tenant details
    expect(body.tenantDetails.id).to.eql(tenantId);
    expect(body.tenantDetails.name).to.eql('Tenant Without Files');

    // Verify contact
    expect(body.contact.email).to.eql('<EMAIL>');

    // Verify files array is empty
    expect(Array.isArray(body.files)).to.be.true();
    expect(body.files).to.be.empty();

    // Verify address
    expect(body.address.street).to.eql('456 No Files St');

    // Verify plan is empty object
    expect(typeof body.plan).to.eql('object');
    expect(body.plan).to.be.empty();
  });

  // Test for GET /tenants/dashboard-metrics reflecting the real service output
  it('invokes GET /tenants/dashboard-metrics and returns status and tenants arrays', async () => {
    const token = getToken([PermissionKey.DashboardMetrics]);
    const metricsMock = {
      status: [
        {count: 5, status: 'ACTIVE'},
        {count: 2, status: 'INACTIVE'},
      ],
      tenants: [
        {id: '1', name: 'Tenant1'},
        {id: '2', name: 'Tenant2'},
      ],
    };
    (tenantMgmtProxyStub.findAllStatusMetrics as sinon.SinonStub).resolves(
      metricsMock.status,
    );

    (tenantMgmtProxyStub.getTenant as sinon.SinonStub).resolves(
      metricsMock.tenants,
    );

    const {body} = await client
      .get(`${basePath}/dashboard-metrics`)
      .set('Authorization', `${token}`);
    // .expect(HTTP_STATUS.OK);

    expect(body).to.have.properties(['status', 'tenants']);
    expect(Array.isArray(body.status)).to.be.true();
    expect(Array.isArray(body.tenants)).to.be.true();
  });
});
