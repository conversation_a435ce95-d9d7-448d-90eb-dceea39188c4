import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {IBillingCycle} from '../../types';

describe('SubscriptionTenureController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      updateSubscription: sinon.stub(),
      createInvoice: sinon.stub(),
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      handleWebhook: sinon.stub(),
      getSoftwareLevel: sinon.stub(),
      suspendSubscriptionById: sinon.stub(),
      cancelSubscriptionById: sinon.stub(),
      renewSubscriptionInvite: sinon.stub(),
      getSubscriptions: sinon.stub(),
      resumeSubscriptionById: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      createPrice: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
      updateConfigureDevices: sinon.stub(),
      findAllBillingStatusMetrics: sinon.stub(),
      getLastUnpaidInvoicePaymentLink: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns list of billing cycles when authorized', async () => {
    const token = getToken([PermissionKey.ViewBillingCycle]);

    const mockBillingCycles: IBillingCycle[] = [
      {
        id: 'cycle-1',
        cycleName: 'Monthly',
        duration: 1,
        durationUnit: 'month',
        description: 'Billed every month',
        deleted: false,
        createdOn: '',
        createdBy: '',
      },
      {
        id: 'cycle-2',
        cycleName: 'Yearly',
        duration: 12,
        durationUnit: 'month',
        description: 'Billed every year',
        deleted: false,
        createdOn: '',
        createdBy: '',
      },
    ];

    subscriptionServiceProxyServiceStub.getSubscriptionTenure.resolves(
      mockBillingCycles,
    );

    const res = await client
      .get('/subscriptions-tenure')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual(mockBillingCycles);
  });

  it('returns empty array when no billing cycles found', async () => {
    subscriptionServiceProxyServiceStub.getSubscriptionTenure.resolves([]);

    const token = getToken([PermissionKey.ViewBillingCycle]);

    const res = await client
      .get('/subscriptions-tenure')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([]);
  });

  it('fails with 401 when no token is provided', async () => {
    await client.get('/subscriptions-tenure').expect(401);
    sinon.assert.notCalled(
      subscriptionServiceProxyServiceStub.getSubscriptionTenure,
    );
  });
});
