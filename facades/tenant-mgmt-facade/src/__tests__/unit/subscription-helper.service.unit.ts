import {
  CryptoHelperService,
  Notification,
  SubscriptionStatus,
  TemplateService,
  TRIAL_PLAN_NAME,
} from '@local/core';
import {expect, sinon} from '@loopback/testlab';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from '../../services/proxies';
import {SubscriptionHelperService} from '../../services/subscription-helper.service';

describe('SubscriptionHelperService', () => {
  let service: SubscriptionHelperService;

  let tenantMgmtProxyServiceStub: TenantMgmtProxyService;
  let subscriptionServiceProxyServiceStub: SubscriptionProxyService;
  let notificationProxyServiceStub: NotificationProxyService;
  let templateService: sinon.SinonStubbedInstance<TemplateService>;

  let cryptoHelperServiceStub: CryptoHelperService;

  const tenant = {
    id: 'tenant-1',
    name: 'Test Tenant',
    planName: TRIAL_PLAN_NAME,
    contacts: [{email: '<EMAIL>'}],
  };

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      updateSubscription: sinon.stub(),
      createInvoice: sinon.stub(),
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      handleWebhook: sinon.stub(),
      createPrice: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(),
      createPlanHistory: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      getSubscriptions: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
      updateConfigureDevices: sinon.stub(),
    };
    tenantMgmtProxyServiceStub = {
      verifyKey: sinon.stub(),
      createTenant: sinon.stub(),
      getTenantById: sinon.stub(),
      getAllTenantStatus: sinon.stub(),
      getTenantCount: sinon.stub(),
      getTenant: sinon.stub(),
      provisionTenant: sinon.stub(),
      updateContactById: sinon.stub(),
      handleWebhookTenantStatus: sinon.stub(),
      updateLeadById: sinon.stub(),
      updateTenantById: sinon.stub(),
      getLeads: sinon.stub(),
      getLeadById: sinon.stub(),
      getLeadsCount: sinon.stub(),
      createLead: sinon.stub(),
      getLeadStatuses: sinon.stub(),
      findAllStatusMetrics: sinon.stub(),
    };
    notificationProxyServiceStub = {
      createBulkNotification: sinon.stub(),
      getTemplateByName: sinon.stub(),
      createNotification: sinon.stub(),
    };
    templateService = sinon.createStubInstance(TemplateService);

    cryptoHelperServiceStub = new CryptoHelperService();

    service = new SubscriptionHelperService(
      cryptoHelperServiceStub,
      subscriptionServiceProxyServiceStub as SubscriptionProxyService,
      tenantMgmtProxyServiceStub as TenantMgmtProxyService,
      notificationProxyServiceStub as NotificationProxyService,
      templateService,
    );
  });

  it('should update subscription status from TRIAL → TRIAL_REMINDER_SENT and send notification', async () => {
    // subscription expiring in 2 days
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 2);

    (tenantMgmtProxyServiceStub.getTenant as sinon.SinonStub).resolves([
      tenant,
    ]);
    (
      subscriptionServiceProxyServiceStub.getSubscriptions as sinon.SinonStub
    ).resolves([
      {
        id: 'sub-1',
        subscriberId: tenant.id,
        trialEndDate,
        status: SubscriptionStatus.TRIAL,
      },
    ]);

    templateService.generateEmail.returns('Trial ending soon email');
    (
      notificationProxyServiceStub.getTemplateByName as sinon.SinonStub
    ).resolves({
      subject: 'Trial ending soon',
      body: 'Hello {{tenantName}}',
    });

    await service.updateTrialPlan();

    sinon.assert.calledWithMatch(
      subscriptionServiceProxyServiceStub.updateSubscription as sinon.SinonStub,
      sinon.match.string,
      {status: SubscriptionStatus.TRIAL_REMINDER_SENT},
      {id: 'sub-1'},
    );

    sinon.assert.calledOnce(
      notificationProxyServiceStub.createBulkNotification as sinon.SinonStub,
    );
    const notificationsArg = (
      notificationProxyServiceStub.createBulkNotification as sinon.SinonStub
    ).getCall(0).args[1];
    expect(notificationsArg[0]).to.be.instanceOf(Notification);
  });

  it('should suspend subscription if TRIAL_REMINDER_SENT and trial expired', async () => {
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() - 1); // expired yesterday

    (tenantMgmtProxyServiceStub.getTenant as sinon.SinonStub).resolves([
      tenant,
    ]);
    (
      subscriptionServiceProxyServiceStub.getSubscriptions as sinon.SinonStub
    ).resolves([
      {
        id: 'sub-2',
        subscriberId: tenant.id,
        trialEndDate,
        status: SubscriptionStatus.TRIAL_REMINDER_SENT,
      },
    ]);

    await service.updateTrialPlan();

    sinon.assert.calledWithMatch(
      subscriptionServiceProxyServiceStub.updateSubscription as sinon.SinonStub,
      sinon.match.string,
      {status: SubscriptionStatus.TRIAL_SUSPEND},
      {id: 'sub-2'},
    );
  });

  it('should expire subscription if TRIAL_SUSPEND and buffer window expired', async () => {
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() - 10); // long expired

    (tenantMgmtProxyServiceStub.getTenant as sinon.SinonStub).resolves([
      tenant,
    ]);
    (
      subscriptionServiceProxyServiceStub.getSubscriptions as sinon.SinonStub
    ).resolves([
      {
        id: 'sub-3',
        subscriberId: tenant.id,
        trialEndDate,
        status: SubscriptionStatus.TRIAL_SUSPEND,
      },
    ]);

    await service.updateTrialPlan();

    sinon.assert.calledWithMatch(
      subscriptionServiceProxyServiceStub.updateSubscription as sinon.SinonStub,
      sinon.match.string,
      {status: SubscriptionStatus.TRIAL_EXPIRED},
      {id: 'sub-3'},
    );
  });

  it('should not send notifications if template not found', async () => {
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 1);

    (tenantMgmtProxyServiceStub.getTenant as sinon.SinonStub).resolves([
      tenant,
    ]);
    (
      subscriptionServiceProxyServiceStub.getSubscriptions as sinon.SinonStub
    ).resolves([
      {
        id: 'sub-4',
        subscriberId: tenant.id,
        trialEndDate,
        status: SubscriptionStatus.TRIAL,
      },
    ]);

    (
      notificationProxyServiceStub.getTemplateByName as sinon.SinonStub
    ).resolves(null);

    await expect(service.updateTrialPlan()).to.be.rejectedWith(
      'Notification template for trial ending not found',
    );
  });

  it('should do nothing if no tenants need notification', async () => {
    (tenantMgmtProxyServiceStub.getTenant as sinon.SinonStub).resolves([
      tenant,
    ]);
    (
      subscriptionServiceProxyServiceStub.getSubscriptions as sinon.SinonStub
    ).resolves([
      {
        id: 'sub-5',
        subscriberId: tenant.id,
        status: SubscriptionStatus.TRIAL,
        trialEndDate: null, // no end date
      },
    ]);

    await service.updateTrialPlan();

    sinon.assert.notCalled(
      notificationProxyServiceStub.createBulkNotification as sinon.SinonStub,
    );
    sinon.assert.notCalled(
      subscriptionServiceProxyServiceStub.updateSubscriptionById as sinon.SinonStub,
    );

    sinon.assert.notCalled(
      notificationProxyServiceStub.createNotification as sinon.SinonStub,
    );
  });
});
