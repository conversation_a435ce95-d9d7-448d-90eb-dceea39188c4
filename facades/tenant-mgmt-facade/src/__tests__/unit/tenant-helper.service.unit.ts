import {expect, sinon} from '@loopback/testlab';
import {Request, HttpErrors} from '@loopback/rest';
import {ILogger} from '@sourceloop/core';
import {FileAdapterService, TenantHelperService} from '../../services';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from '../../services/proxies';
import {
  CreateTenantDTO,
  CustomerDto,
  Plan,
  Subscription,
  Tenant,
  VerifyKeyDto,
  TriggerDto,
} from '../../models';
import {
  StatusDto,
  SubscriptionStatus,
  TemplateService,
  CryptoHelperService as localCryptoService,
} from '@local/core';
import {CryptoHelperService} from '@sourceloop/ctrl-plane-tenant-management-service';
import {Route53ClientProxyService} from '../../services/proxies/route-53-client-proxy.service';
import {AnyObject} from '@loopback/repository';
import {ApplicationWebhookService} from '../../services/application-webhook.service';

interface TestContact {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

interface TestAddress {
  id: string;
  street: string;
  city: string;
  state: string;
}

describe('TenantHelperService (Unit)', () => {
  let service: TenantHelperService;
  let mockRequest: Request;
  let fileAdapterServiceStub: Partial<FileAdapterService>;
  let tenantMgmtProxyServiceStub: Partial<TenantMgmtProxyService>;
  let subscriptionServiceProxyServiceStub: Partial<SubscriptionProxyService>;
  let loggerStub: ILogger;
  let cryptoHelperServiceStub: CryptoHelperService;
  let route53ClientProxyServiceStub: Partial<Route53ClientProxyService>;
  let notificationProxyServiceStub: Partial<NotificationProxyService>;
  let templateServiceStub: Partial<TemplateService>;
  let localCryptoServiceStub: Partial<localCryptoService>;
  let applicationWebhookServiceStub: Partial<ApplicationWebhookService>;

  const token = 'Bearer test-token';

  beforeEach(() => {
    mockRequest = {
      headers: {authorization: token},
    } as unknown as Request;

    fileAdapterServiceStub = {
      checkIfKeyExists: sinon.stub().resolves(true),
      generateFileResponse: sinon.stub(),
      generateLocalStackViewUrl: sinon.stub(),
      appendOrUpdateTenantRowToS3: sinon.stub().resolves(),
      // Ensure generateFileInfoWithSignedUrl is always a stub for getTenantCsvFile tests
      generateFileInfoWithSignedUrl: sinon.stub(),
    };

    tenantMgmtProxyServiceStub = {
      verifyKey: sinon.stub(),
      createTenant: sinon.stub(),
      getTenantById: sinon.stub(),
      getAllTenantStatus: sinon.stub(),
      getTenantCount: sinon.stub(),
      getTenant: sinon.stub(),
      provisionTenant: sinon.stub(),
      findAllStatusMetrics: sinon.stub(),
    };
    cryptoHelperServiceStub = new CryptoHelperService();
    localCryptoServiceStub = {
      generateHmacSHA256: sinon.stub(),
    };
    applicationWebhookServiceStub = {
      updateContact: sinon.stub(),
    };

    subscriptionServiceProxyServiceStub = {
      updateSubscription: sinon.stub(),
      createInvoice: sinon.stub(),
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
    };

    loggerStub = {
      error: sinon.stub(),
      info: sinon.stub(),
      debug: sinon.stub(),
      warn: sinon.stub(),
      log: sinon.stub(),
    };
    notificationProxyServiceStub = {
      getTemplateByName: sinon.stub(),
      createNotification: sinon.stub(),
    };
    templateServiceStub = {
      generateEmail: sinon.stub(),
    };

    route53ClientProxyServiceStub = {
      checkSubdomain: sinon.stub().resolves(false),
    };

    service = new TenantHelperService(
      mockRequest,
      fileAdapterServiceStub as FileAdapterService,
      tenantMgmtProxyServiceStub as TenantMgmtProxyService,
      subscriptionServiceProxyServiceStub as SubscriptionProxyService,
      cryptoHelperServiceStub,
      loggerStub,
      route53ClientProxyServiceStub as Route53ClientProxyService,
      notificationProxyServiceStub as NotificationProxyService,
      templateServiceStub as TemplateService,
      localCryptoServiceStub as localCryptoService,
      applicationWebhookServiceStub as ApplicationWebhookService,
    );
  });

  afterEach(() => sinon.restore());

  it('verifyKey should call tenantMgmtProxyService.verifyKey', async () => {
    (tenantMgmtProxyServiceStub.verifyKey as sinon.SinonStub).resolves({
      available: true,
    });

    const res = await service.verifyKey(new VerifyKeyDto({key: 'abc'}));
    sinon.assert.calledOnceWithExactly(
      tenantMgmtProxyServiceStub.verifyKey as sinon.SinonStub,
      token,
      sinon.match.has('key', 'abc'),
    );
    expect(res.available).to.be.true();
  });

  it('createTenant should call proxies correctly', async () => {
    const dto = {
      planId: 'plan-123',
      contact: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        designation: 'Manager',
      },
      files: [{fileName: 'doc.pdf'}],
      name: 'TestTenant',
      key: 'test-key',
      overallPlan: {
        planId: 'plan-id',
      },
      isTrialApplied: true,
      numberOfUsers: 10,
      totalCost: 1000,
    } as unknown as CreateTenantDTO;

    // Stubs for all proxies
    (
      subscriptionServiceProxyServiceStub.findPlanById as sinon.SinonStub
    ).resolves(
      new Plan({
        id: 'plan-123',
        name: 'Test Plan',
        productRefId: 'prod-1',
        billingCycle: {
          id: 'bc-1',
          cycleName: 'Monthly',
          durationUnit: 'month',
          duration: 1,
          getId: () => 'bc-1',
          getIdObject: () => ({id: 'bc-1'}),
          toJSON: () => ({}),
          toObject: () => ({}),
        },
        currency: {
          id: 'cur-1',
          currencyCode: 'USD',
          currencyName: 'US Dollar',
          getId: () => 'cur-1',
          getIdObject: () => ({id: 'cur-1'}),
          toJSON: () => ({}),
          toObject: () => ({}),
        },
      }),
    );
    (fileAdapterServiceStub.generateFileResponse as sinon.SinonStub).resolves([
      {fileName: 'doc.pdf'},
    ]);
    (tenantMgmtProxyServiceStub.createTenant as sinon.SinonStub).resolves(
      new Tenant({id: '1', name: 'TestTenant'}),
    );
    // Ensure createCustomer is a stub
    if (
      !subscriptionServiceProxyServiceStub.createCustomer ||
      typeof (
        subscriptionServiceProxyServiceStub.createCustomer as unknown as {
          resolves?: Function;
        }
      ).resolves !== 'function'
    ) {
      subscriptionServiceProxyServiceStub.createCustomer = sinon.stub();
    }
    (
      subscriptionServiceProxyServiceStub.createCustomer as sinon.SinonStub
    ).resolves({id: 'cust-123'} as unknown as CustomerDto);
    // Ensure createSubscriptions is a stub
    if (
      !subscriptionServiceProxyServiceStub.createSubscriptions ||
      typeof (
        subscriptionServiceProxyServiceStub.createSubscriptions as unknown as {
          resolves?: Function;
        }
      ).resolves !== 'function'
    ) {
      subscriptionServiceProxyServiceStub.createSubscriptions = sinon.stub();
    }
    (
      subscriptionServiceProxyServiceStub.createSubscriptions as sinon.SinonStub
    ).resolves({
      id: 'sub-123',
      subscriberId: '1',
      planId: 'plan-123',
      status: SubscriptionStatus.TRIAL,
      startDate: '2025-01-01',
      endDate: '2025-01-15',
    } as unknown as Subscription);
    (tenantMgmtProxyServiceStub.provisionTenant as sinon.SinonStub).resolves();
    (
      subscriptionServiceProxyServiceStub.createInvoice as sinon.SinonStub
    ).resolves();

    const res = await service.createTenant(dto);

    // Assert all proxies called with correct arguments
    sinon.assert.calledOnceWithExactly(
      subscriptionServiceProxyServiceStub.findPlanById as sinon.SinonStub,
      token,
      'plan-123',
      sinon.match.object,
    );
    sinon.assert.calledOnce(
      fileAdapterServiceStub.generateFileResponse as sinon.SinonStub,
    );
    sinon.assert.calledOnceWithExactly(
      tenantMgmtProxyServiceStub.createTenant as sinon.SinonStub,
      token,
      sinon.match.object,
      undefined,
    );
    sinon.assert.calledOnceWithExactly(
      subscriptionServiceProxyServiceStub.createCustomer as sinon.SinonStub,
      token,
      sinon.match({
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '1234567890',
        name: 'TestTenant',
      }),
      '1',
    );
    sinon.assert.calledOnce(
      subscriptionServiceProxyServiceStub.createSubscriptions as sinon.SinonStub,
    );
    sinon.assert.calledOnce(
      tenantMgmtProxyServiceStub.provisionTenant as sinon.SinonStub,
    );
    sinon.assert.calledOnce(
      subscriptionServiceProxyServiceStub.createInvoice as sinon.SinonStub,
    );

    expect(res).to.be.instanceOf(Tenant);
    expect(res.name).to.eql('TestTenant');
  });

  it('createTenant should throw BadRequest on invalid contact JSON', async () => {
    const badDto = {
      planId: 'p1',
      contact: '{invalid}',
      files: [],
    } as unknown as CreateTenantDTO;

    await expect(service.createTenant(badDto)).to.be.rejectedWith(
      HttpErrors.BadRequest,
    );
    sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
  });

  describe('getTenantDetails', () => {
    it('should retrieve tenant details with files and signed URLs', async () => {
      const tenantId = 'tenant-123';
      const mockTenant = {
        id: tenantId,
        name: 'Test Tenant',
        key: 'test-key',
        contacts: [
          {
            id: 'contact-1',
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
          },
        ],
        files: [
          {
            id: 'file-1',
            fileKey: '1754137174168',
            originalName: 'contract.pdf',
            source: 1,
            size: 582613,
            tenantId: tenantId,
          },
          {
            id: 'file-2',
            fileKey: '1754137174169',
            originalName: 'invoice.pdf',
            source: 1,
            size: 20700,
            tenantId: tenantId,
          },
        ],
        address: {
          id: 'address-1',
          street: '123 Main St',
          city: 'Test City',
          state: 'Test State',
        },
      };

      const expectedSignedUrl1 =
        'http://localhost:4566/bucket/file1?signed=true';
      const expectedSignedUrl2 =
        'http://localhost:4566/bucket/file2?signed=true';

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub)
        .onFirstCall()
        .resolves(expectedSignedUrl1)
        .onSecondCall()
        .resolves(expectedSignedUrl2);

      const result = await service.getTenantDetails(tenantId);

      // Verify proxy service was called correctly
      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub,
        token,
        tenantId,
      );

      // Verify file adapter service was called for each file
      sinon.assert.calledTwice(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
      );
      sinon.assert.calledWithExactly(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
        '1754137174168',
        'contract.pdf',
      );
      sinon.assert.calledWithExactly(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
        '1754137174169',
        'invoice.pdf',
      );

      // Verify logging calls
      sinon.assert.calledWith(
        loggerStub.info as sinon.SinonStub,
        'Found 2 files for tenant tenant-123',
      );
      sinon.assert.calledWith(
        loggerStub.info as sinon.SinonStub,
        'File keys found: 1754137174168, 1754137174169',
      );

      // Verify result structure
      expect(result).to.have.properties([
        'tenantDetails',
        'contact',
        'files',
        'address',
        'plan',
      ]);
      expect(result.tenantDetails).to.have.properties(['id', 'name', 'key']);
      expect(result.tenantDetails.id).to.eql(tenantId);
      expect(result.tenantDetails.name).to.eql('Test Tenant');

      // Verify contact extraction (first contact)
      expect(result.contact).to.have.properties([
        'id',
        'email',
        'firstName',
        'lastName',
      ]);
      expect((result.contact as TestContact).email).to.eql('<EMAIL>');

      // Verify files with signed URLs
      expect(Array.isArray(result.files)).to.be.true();
      expect(result.files).to.have.length(2);
      expect(result.files[0]).to.have.property('signedUrl', expectedSignedUrl1);
      expect(result.files[0]).to.have.property('originalName', 'contract.pdf');
      expect(result.files[1]).to.have.property('signedUrl', expectedSignedUrl2);
      expect(result.files[1]).to.have.property('originalName', 'invoice.pdf');

      // Verify address
      expect(result.address).to.have.properties([
        'id',
        'street',
        'city',
        'state',
      ]);
      expect((result.address as TestAddress).street).to.eql('123 Main St');

      // Verify plan is empty object
      expect(typeof result.plan).to.eql('object');
      expect(result.plan).to.be.empty();
    });

    it('should handle tenant with no contacts', async () => {
      const tenantId = 'tenant-no-contacts';
      const mockTenant = {
        id: tenantId,
        name: 'No Contacts Tenant',
        contacts: [],
        files: [],
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );

      const result = await service.getTenantDetails(tenantId);

      expect(typeof result.contact).to.eql('object');
      expect(result.contact).to.be.empty();
      expect(Array.isArray(result.files)).to.be.true();
      expect(result.files).to.be.empty();
    });

    it('should handle tenant with no files', async () => {
      const tenantId = 'tenant-no-files';
      const mockTenant = {
        id: tenantId,
        name: 'No Files Tenant',
        contacts: [{id: 'contact-1', email: '<EMAIL>'}],
        files: [],
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );

      const result = await service.getTenantDetails(tenantId);

      expect(Array.isArray(result.files)).to.be.true();
      expect(result.files).to.be.empty();
      sinon.assert.calledWith(
        loggerStub.info as sinon.SinonStub,
        'Found 0 files for tenant tenant-no-files',
      );
      sinon.assert.notCalled(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
      );
    });

    it('should handle signed URL generation error gracefully', async () => {
      const tenantId = 'tenant-url-error';
      const mockTenant = {
        id: tenantId,
        name: 'URL Error Tenant',
        contacts: [],
        files: [
          {
            id: 'file-1',
            fileKey: 'error-file',
            originalName: 'error.pdf',
            source: 1,
            size: 1000,
          },
        ],
      };

      const urlError = new Error('Failed to generate signed URL');
      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub
      ).rejects(urlError);

      const result = await service.getTenantDetails(tenantId);

      // Verify error was logged
      sinon.assert.calledWith(
        loggerStub.error as sinon.SinonStub,
        'Error generating LocalStack view URL for file error.pdf:',
        urlError,
      );

      // Verify file has null signedUrl
      expect(result.files).to.have.length(1);
      expect(result.files[0]).to.have.property('signedUrl', null);
      expect(result.files[0]).to.have.property('originalName', 'error.pdf');
    });
  });

  describe('getAllStatuses', () => {
    it('should retrieve all tenant statuses', async () => {
      const mockStatuses: StatusDto[] = [
        new StatusDto({
          statuses: {
            1: 'ACTIVE',
            2: 'INACTIVE',
            3: 'SUSPENDED',
          },
        }),
      ];

      (
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub
      ).resolves(mockStatuses);

      const result = await service.getAllStatuses();

      // Verify proxy service was called correctly
      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub,
        token,
      );

      // Verify result
      expect(Array.isArray(result)).to.be.true();
      expect(result).to.have.length(1);
      expect(result).to.eql(mockStatuses);
      expect(result[0]).to.have.property('statuses');
      expect(result[0].statuses).to.have.property('1', 'ACTIVE');
      expect(result[0].statuses).to.have.property('2', 'INACTIVE');
      expect(result[0].statuses).to.have.property('3', 'SUSPENDED');
    });

    it('should handle empty status list', async () => {
      (
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub
      ).resolves([]);

      const result = await service.getAllStatuses();

      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub,
        token,
      );

      expect(Array.isArray(result)).to.be.true();
      expect(result).to.be.empty();
    });

    it('should propagate errors from proxy service', async () => {
      const proxyError = new Error('Proxy service error');
      (
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub
      ).rejects(proxyError);

      await expect(service.getAllStatuses()).to.be.rejectedWith(
        'Proxy service error',
      );

      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub,
        token,
      );
    });
  });

  describe('TenantHelperService uncovered methods', () => {
    let subscriptionProxyServiceStub: Partial<SubscriptionProxyService>;

    beforeEach(() => {
      mockRequest = {headers: {authorization: token}} as unknown as Request;
      fileAdapterServiceStub = {
        generateFileResponse: sinon.stub(),
        checkIfKeyExists: sinon.stub().resolves(true),
        appendOrUpdateTenantRowToS3: sinon.stub().resolves(),
      };
      tenantMgmtProxyServiceStub = {
        getTenantById: sinon.stub(),
        updateTenant: sinon.stub(),
        updateTenantById: sinon.stub(),
        triggerPipeline: sinon.stub(),
        provisionTenant: sinon.stub(),
        getTenantCount: sinon.stub(),
        getTenant: sinon.stub(),
      };
      subscriptionProxyServiceStub = {
        renewSubscriptionInvite: sinon.stub(),
        getSubscriptions: sinon.stub(),
        cancelSubscriptionById: sinon.stub(),
        resumeSubscriptionById: sinon.stub(),
        findPlanById: sinon.stub(),
        updateSubscriptionById: sinon.stub(),
        createPrice: sinon.stub(),
      };
      loggerStub = {
        error: sinon.stub(),
        info: sinon.stub(),
        debug: sinon.stub(),
        warn: sinon.stub(),
        log: sinon.stub(),
      };
      cryptoHelperServiceStub = new CryptoHelperService();
      route53ClientProxyServiceStub = {
        checkSubdomain: sinon.stub().resolves(true),
      };

      notificationProxyServiceStub = {
        getTemplateByName: sinon.stub(),
        createNotification: sinon.stub(),
      };
      templateServiceStub = {
        generateEmail: sinon.stub(),
      };
      service = new TenantHelperService(
        mockRequest,
        fileAdapterServiceStub as FileAdapterService,
        tenantMgmtProxyServiceStub as TenantMgmtProxyService,
        subscriptionProxyServiceStub as SubscriptionProxyService,
        cryptoHelperServiceStub,
        loggerStub,
        route53ClientProxyServiceStub as Route53ClientProxyService,
        notificationProxyServiceStub as NotificationProxyService,
        templateServiceStub as TemplateService,
        localCryptoServiceStub as localCryptoService,
        applicationWebhookServiceStub as ApplicationWebhookService,
      );
    });

    afterEach(() => sinon.restore());

    it('should call triggerPipeline and throw if tenant not found', async () => {
      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        null,
      );
      const triggerDto = new TriggerDto({detailType: 'test', payload: {}});
      await expect(
        service.triggerPipeline('id', triggerDto),
      ).to.be.rejectedWith(HttpErrors.NotFound);
    });

    it('should call triggerPipeline and succeed if tenant found', async () => {
      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves({
        id: 'id',
      });
      (
        tenantMgmtProxyServiceStub.triggerPipeline as sinon.SinonStub
      ).resolves();
      const triggerDto = new TriggerDto({detailType: 'test', payload: {}});
      await service.triggerPipeline('id', triggerDto);
      sinon.assert.calledOnce(
        tenantMgmtProxyServiceStub.triggerPipeline as sinon.SinonStub,
      );
    });

    it('should call sendInvite and update tenant', async () => {
      (
        subscriptionProxyServiceStub.renewSubscriptionInvite as sinon.SinonStub
      ).resolves();
      (tenantMgmtProxyServiceStub.updateTenant as sinon.SinonStub).resolves();
      await service.sendInvite('id');
      sinon.assert.calledOnce(
        subscriptionProxyServiceStub.renewSubscriptionInvite as sinon.SinonStub,
      );
      sinon.assert.calledOnce(
        tenantMgmtProxyServiceStub.updateTenant as sinon.SinonStub,
      );
    });

    it('should call triggerDeProvision and cancel subscription', async () => {
      (
        subscriptionProxyServiceStub.getSubscriptions as sinon.SinonStub
      ).resolves([{id: 'subid', status: 'ACTIVE'}]);
      (
        subscriptionProxyServiceStub.cancelSubscriptionById as sinon.SinonStub
      ).resolves();
      (
        tenantMgmtProxyServiceStub.triggerPipeline as sinon.SinonStub
      ).resolves();
      await service.triggerDeProvision('id');
      sinon.assert.calledOnce(
        subscriptionProxyServiceStub.cancelSubscriptionById as sinon.SinonStub,
      );
      sinon.assert.calledOnce(
        tenantMgmtProxyServiceStub.triggerPipeline as sinon.SinonStub,
      );
    });

    it('should cover isTrialApplied=false branch in createTenant', async () => {
      // Setup DTO and stubs for non-trial path
      const dto = {
        planId: 'plan-123',
        contact: {
          firstName: 'A',
          lastName: 'B',
          email: '<EMAIL>',
          phoneNumber: '123',
          designation: 'dev',
        },
        files: [],
        name: 'TestTenant',
        key: 'test-key',
        overallPlan: {planId: 'plan-id'},
        isTrialApplied: false,
        numberOfUsers: 5,
        totalCost: 100,
      } as unknown as CreateTenantDTO;
      const tenant = {id: 'tenant-1', name: 'TestTenant'};
      const selectedPlan = {
        id: 'plan-123',
        name: 'Test Plan',
        productRefId: 'prod-1',
        billingCycle: {durationUnit: 'month', duration: 1},
        currency: {currencyCode: 'USD'},
      };
      const customer = {id: 'cust-1'};
      const price = {id: 'price-1'};

      // Fix: Ensure all required stubs exist and are sinon stubs (for dist build)
      if (
        !subscriptionProxyServiceStub.createCustomer ||
        typeof (
          subscriptionProxyServiceStub.createCustomer as unknown as {
            resolves?: Function;
          }
        ).resolves !== 'function'
      ) {
        subscriptionProxyServiceStub.createCustomer = sinon.stub();
      }
      if (
        !subscriptionProxyServiceStub.createPrice ||
        typeof (
          subscriptionProxyServiceStub.createPrice as unknown as {
            resolves?: Function;
          }
        ).resolves !== 'function'
      ) {
        subscriptionProxyServiceStub.createPrice = sinon.stub();
      }
      if (
        !subscriptionProxyServiceStub.createSubscriptions ||
        typeof (
          subscriptionProxyServiceStub.createSubscriptions as unknown as {
            resolves?: Function;
          }
        ).resolves !== 'function'
      ) {
        subscriptionProxyServiceStub.createSubscriptions = sinon.stub();
      }

      (fileAdapterServiceStub.generateFileResponse as sinon.SinonStub).resolves(
        [],
      );
      // Fix: Ensure tenantMgmtProxyServiceStub.createTenant is a stub before calling .resolves
      if (
        !tenantMgmtProxyServiceStub.createTenant ||
        typeof (
          tenantMgmtProxyServiceStub.createTenant as unknown as {
            resolves?: Function;
          }
        ).resolves !== 'function'
      ) {
        tenantMgmtProxyServiceStub.createTenant = sinon.stub();
      }
      (tenantMgmtProxyServiceStub.createTenant as sinon.SinonStub).resolves(
        tenant,
      );
      (subscriptionProxyServiceStub.createCustomer as sinon.SinonStub).resolves(
        customer,
      );
      // Fix: Ensure findPlanById stub exists before calling .resolves
      if (
        !subscriptionProxyServiceStub.findPlanById ||
        typeof (
          subscriptionProxyServiceStub.findPlanById as unknown as {
            resolves?: Function;
          }
        ).resolves !== 'function'
      ) {
        subscriptionProxyServiceStub.findPlanById = sinon.stub();
      }
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        selectedPlan,
      );
      (subscriptionProxyServiceStub.createPrice as sinon.SinonStub).resolves(
        price,
      );
      (
        subscriptionProxyServiceStub.createSubscriptions as sinon.SinonStub
      ).resolves({
        id: 'sub-1',
        subscriberId: 'tenant-1',
        planId: 'plan-123',
        status: SubscriptionStatus.PENDING,
      });

      const result = await service.createTenant(dto);
      expect(result).to.eql(tenant);
    });

    // Additional coverage for error and edge cases in TenantHelperService

    it('should throw error if selected plan does not exist in createTenant', async () => {
      const dto = {
        planId: 'non-existent-plan',
        contact: {email: '<EMAIL>'},
        files: [],
        name: 'TestTenant',
        key: 'test-key',
        overallPlan: {planId: 'plan-id'},
        isTrialApplied: false,
        numberOfUsers: 5,
        totalCost: 100,
      } as unknown as CreateTenantDTO;
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        undefined,
      );
      await expect(service.createTenant(dto)).to.be.rejectedWith(
        'selected plan does not exist',
      );
    });

    it('should throw BadRequest if contact is invalid JSON in _verifyPayload', () => {
      const badDto = {contact: '{bad}'};
      expect(() =>
        (
          service as unknown as {_verifyPayload: (dto: unknown) => unknown}
        )._verifyPayload(badDto),
      ).to.throw(HttpErrors.BadRequest);
      sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
    });

    it('should set phoneNumber and designation to undefined if empty string in _verifyPayload', () => {
      const dto = {contact: {phoneNumber: '', designation: ''}};
      (
        service as unknown as {_verifyPayload: (dto: unknown) => unknown}
      )._verifyPayload(dto);
      expect(dto.contact.phoneNumber).to.be.undefined();
      expect(dto.contact.designation).to.be.undefined();
    });

    it('should throw BadRequest if existingFiles is invalid JSON in parseExistingFiles', () => {
      expect(() =>
        (
          service as unknown as {parseExistingFiles: (dto: unknown) => unknown}
        ).parseExistingFiles({existingFiles: '{bad}'}),
      ).to.throw(HttpErrors.BadRequest);
      sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
    });

    it('should return parsed files if existingFiles is valid JSON in parseExistingFiles', () => {
      const files = [
        {fileKey: 'key', originalName: 'name', source: 1, size: 1},
      ];
      const dto = {existingFiles: JSON.stringify(files)};
      const result = (
        service as unknown as {parseExistingFiles: (dto: unknown) => unknown}
      ).parseExistingFiles(dto);
      expect(result).to.be.Array();
      const filesResult = result as Array<{fileKey: string}>;
      expect(filesResult[0]).to.have.property('fileKey', 'key');
    });
    it('should call reActivateTenant and update tenant', async () => {
      (
        subscriptionProxyServiceStub.renewSubscriptionInvite as sinon.SinonStub
      ).resolves();
      (tenantMgmtProxyServiceStub.updateTenant as sinon.SinonStub).resolves();
      await service.reActivateTenant('id');
      sinon.assert.calledOnce(
        subscriptionProxyServiceStub.renewSubscriptionInvite as sinon.SinonStub,
      );
      sinon.assert.calledOnce(
        tenantMgmtProxyServiceStub.updateTenant as sinon.SinonStub,
      );
    });

    it('should call triggerProvision and resume subscription', async () => {
      sinon
        .stub(
          service as unknown as {findSuspendedSubscription: Function},
          'findSuspendedSubscription',
        )
        .resolves({id: 'subid', subscriberId: 'sid', plan: {}, planId: 'pid'});
      sinon
        .stub(
          service as unknown as {buildSubscriptionDto: Function},
          'buildSubscriptionDto',
        )
        .returns({});
      (
        tenantMgmtProxyServiceStub.provisionTenant as sinon.SinonStub
      ).resolves();
      (
        subscriptionProxyServiceStub.resumeSubscriptionById as sinon.SinonStub
      ).resolves();
      await service.triggerProvision('id');
      sinon.assert.calledOnce(
        subscriptionProxyServiceStub.resumeSubscriptionById as sinon.SinonStub,
      );
    });

    describe('TenantHelperService uncovered branches and errors', () => {
      beforeEach(() => {
        mockRequest = {headers: {authorization: token}} as unknown as Request;
        fileAdapterServiceStub = {
          generateFileResponse: sinon.stub(),
          appendOrUpdateTenantRowToS3: sinon.stub().resolves(),
        };
        tenantMgmtProxyServiceStub = {
          getTenantById: sinon.stub(),
          updateTenant: sinon.stub(),
          updateTenantById: sinon.stub(),
          triggerPipeline: sinon.stub(),
          provisionTenant: sinon.stub(),
          getTenantCount: sinon.stub(),
          getTenant: sinon.stub(),
          getAllTenantStatus: sinon.stub(),
          verifyKey: sinon.stub(),
        };
        subscriptionProxyServiceStub = {
          renewSubscriptionInvite: sinon.stub(),
          getSubscriptions: sinon.stub(),
          cancelSubscriptionById: sinon.stub(),
          resumeSubscriptionById: sinon.stub(),
          findPlanById: sinon.stub(),
          updateSubscriptionById: sinon.stub(),
          createPrice: sinon.stub(),
          createCustomer: sinon.stub(),
          createInvoice: sinon.stub(),
          createSubscriptions: sinon.stub(),
        };
        loggerStub = {
          error: sinon.stub(),
          info: sinon.stub(),
          debug: sinon.stub(),
          warn: sinon.stub(),
          log: sinon.stub(),
        };
        cryptoHelperServiceStub = new CryptoHelperService();
        route53ClientProxyServiceStub = {
          checkSubdomain: sinon.stub().resolves(false),
        };

        notificationProxyServiceStub = {
          getTemplateByName: sinon.stub(),
          createNotification: sinon.stub(),
        };
        templateServiceStub = {
          generateEmail: sinon.stub(),
        };
        service = new TenantHelperService(
          mockRequest,
          fileAdapterServiceStub as FileAdapterService,
          tenantMgmtProxyServiceStub as TenantMgmtProxyService,
          subscriptionProxyServiceStub as SubscriptionProxyService,
          cryptoHelperServiceStub,
          loggerStub,
          route53ClientProxyServiceStub as Route53ClientProxyService,
          notificationProxyServiceStub as NotificationProxyService,
          templateServiceStub as TemplateService,
          localCryptoServiceStub as localCryptoService,
          applicationWebhookServiceStub as ApplicationWebhookService,
        );
      });

      afterEach(() => sinon.restore());

      it('should throw Unauthorized if no authorization header', () => {
        expect(
          () =>
            new TenantHelperService(
              {headers: {}} as unknown as Request,
              fileAdapterServiceStub as FileAdapterService,
              tenantMgmtProxyServiceStub as TenantMgmtProxyService,
              subscriptionProxyServiceStub as SubscriptionProxyService,
              cryptoHelperServiceStub,
              loggerStub,
              route53ClientProxyServiceStub as Route53ClientProxyService,
              notificationProxyServiceStub as NotificationProxyService,
              templateServiceStub as TemplateService,
              localCryptoServiceStub as localCryptoService,
              applicationWebhookServiceStub as ApplicationWebhookService,
            ),
        ).to.throw(HttpErrors.Unauthorized);
      });

      it('should throw NotFound if getSubscriptions returns empty in triggerDeProvision', async () => {
        (
          subscriptionProxyServiceStub.getSubscriptions as sinon.SinonStub
        ).resolves([]);
        await expect(service.triggerDeProvision('id')).to.be.rejectedWith(
          HttpErrors.NotFound,
        );
      });

      it('should throw NotFound if getSubscriptions returns empty in findSuspendedSubscription', async () => {
        // Patch the method directly to stub getSubscriptions
        service['subscriptionProxyService'].getSubscriptions = sinon
          .stub()
          .resolves([]);
        await expect(
          service['findSuspendedSubscription']('id'),
        ).to.be.rejectedWith(HttpErrors.NotFound);
      });

      it('should throw InternalServerError if HOSTED_ZONE_ID is not set in verifyKey', async () => {
        const oldEnv = process.env.HOSTED_ZONE_ID;
        delete process.env.HOSTED_ZONE_ID;
        const verifyKeyDto = new VerifyKeyDto({key: 'abc'});
        await expect(service.verifyKey(verifyKeyDto)).to.be.rejectedWith(
          HttpErrors.InternalServerError,
        );
        process.env.HOSTED_ZONE_ID = oldEnv;
      });

      it('should throw InternalServerError if DOMAIN is not set in verifyKey', async () => {
        process.env.HOSTED_ZONE_ID = 'zone';
        const oldEnv = process.env.DOMAIN;
        delete process.env.DOMAIN;
        const verifyKeyDto = new VerifyKeyDto({key: 'abc'});
        await expect(service.verifyKey(verifyKeyDto)).to.be.rejectedWith(
          HttpErrors.InternalServerError,
        );
        process.env.DOMAIN = oldEnv;
      });

      it('should return keySuggestionDto if subdomain available but not available', async () => {
        process.env.HOSTED_ZONE_ID = 'zone';
        process.env.DOMAIN = 'domain.com';
        (tenantMgmtProxyServiceStub.verifyKey as sinon.SinonStub).resolves({
          available: false,
        });
        route53ClientProxyServiceStub.checkSubdomain = sinon
          .stub()
          .resolves(true);
        const verifyKeyDto = new VerifyKeyDto({key: 'abc'});
        const res = await service.verifyKey(verifyKeyDto);
        expect(res.available).to.be.false();
      });

      it('should throw BadRequest if contact is invalid JSON in _verifyPayload', () => {
        const badDto = {contact: '{bad}'};
        expect(() =>
          (
            service as unknown as {_verifyPayload: (dto: unknown) => unknown}
          )._verifyPayload(badDto),
        ).to.throw(HttpErrors.BadRequest);
        sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
      });

      it('should set phoneNumber and designation to undefined if empty string in _verifyPayload', () => {
        const dto = {contact: {phoneNumber: '', designation: ''}};
        (
          service as unknown as {_verifyPayload: (dto: unknown) => unknown}
        )._verifyPayload(dto);
        expect(dto.contact.phoneNumber).to.be.undefined();
        expect(dto.contact.designation).to.be.undefined();
      });

      it('should throw BadRequest if existingFiles is invalid JSON in parseExistingFiles', () => {
        expect(() =>
          (
            service as unknown as {
              parseExistingFiles: (dto: unknown) => unknown;
            }
          ).parseExistingFiles({existingFiles: '{bad}'}),
        ).to.throw(HttpErrors.BadRequest);
        sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
      });

      it('should return parsed files if existingFiles is valid JSON in parseExistingFiles', () => {
        const files = [
          {fileKey: 'key', originalName: 'name', source: 1, size: 1},
        ];
        const dto = {existingFiles: JSON.stringify(files)};
        const result = (
          service as unknown as {parseExistingFiles: (dto: unknown) => unknown}
        ).parseExistingFiles(dto);
        expect(result).to.be.Array();
        const filesResult = result as Array<{fileKey: string}>;
        expect(filesResult[0]).to.have.property('fileKey', 'key');
      });

      it('should handle error in generateLocalStackViewUrl in getTenantDetails', async () => {
        (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves({
          id: 'id',
          contacts: [],
          files: [{fileKey: 'key', originalName: 'name', source: 1, size: 1}],
          address: {},
        });
        // Patch the service instance directly for generateLocalStackViewUrl
        service['fileAdapterService'].generateLocalStackViewUrl = sinon
          .stub()
          .rejects(new Error('fail'));
        const result = await service.getTenantDetails('id');
        expect(result.files[0].signedUrl).to.be.null();
        sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
      });

      it('should call getAllStatuses and propagate error', async () => {
        (
          tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub
        ).rejects(new Error('fail'));
        await expect(service.getAllStatuses()).to.be.rejectedWith('fail');
      });

      it('should call countTenants', async () => {
        (tenantMgmtProxyServiceStub.getTenantCount as sinon.SinonStub).resolves(
          {
            count: 1,
          },
        );
        const result = await service.countTenants();
        expect(result).to.have.property('count', 1);
      });

      it('should call getTenants', async () => {
        (tenantMgmtProxyServiceStub.getTenant as sinon.SinonStub).resolves([
          {id: 'tid'},
        ]);
        const result = await service.getTenants();
        expect(result).to.be.an.Array();
        expect(result[0]).to.have.property('id', 'tid');
      });
    });
  });
  describe('getTenantCsvFile', () => {
    it('should return file info when file exists', async () => {
      const fileInfo = {url: 'signed-url', fileKey: 'tenant.csv'};
      // Ensure stub exists before using resolves
      if (
        !fileAdapterServiceStub.generateFileInfoWithSignedUrl ||
        typeof (
          fileAdapterServiceStub.generateFileInfoWithSignedUrl as unknown as {
            resolves?: Function;
          }
        ).resolves !== 'function'
      ) {
        fileAdapterServiceStub.generateFileInfoWithSignedUrl = sinon.stub();
      }
      (
        fileAdapterServiceStub.generateFileInfoWithSignedUrl as sinon.SinonStub
      ).resolves(fileInfo);
      const result = await service.getTenantCsvFile();
      expect(result).to.eql(fileInfo);
      sinon.assert.calledOnceWithExactly(
        fileAdapterServiceStub.generateFileInfoWithSignedUrl as sinon.SinonStub,
        sinon.match.string,
      );
    });

    it('should create CSV and retry if NotFound error, then succeed', async () => {
      const notFoundError = {name: 'NotFound'};
      const fileInfo = {url: 'signed-url', fileKey: 'tenant.csv'};
      // Ensure stub exists before using onFirstCall/onSecondCall
      let generateStub =
        fileAdapterServiceStub.generateFileInfoWithSignedUrl as sinon.SinonStub;
      if (
        !fileAdapterServiceStub.generateFileInfoWithSignedUrl ||
        typeof generateStub.onFirstCall !== 'function'
      ) {
        fileAdapterServiceStub.generateFileInfoWithSignedUrl = sinon.stub();
        generateStub =
          fileAdapterServiceStub.generateFileInfoWithSignedUrl as sinon.SinonStub;
      }
      generateStub.onFirstCall().rejects(notFoundError);
      generateStub.onSecondCall().resolves(fileInfo);
      sinon.stub(service as AnyObject, 'createCSVIfNotExists').resolves();

      const result = await service.getTenantCsvFile();
      expect(result).to.eql(fileInfo);
      sinon.assert.calledTwice(generateStub);
      sinon.assert.calledOnce((service as AnyObject).createCSVIfNotExists);
    });
  });
});
