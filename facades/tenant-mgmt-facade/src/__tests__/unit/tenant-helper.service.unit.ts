import {expect, sinon} from '@loopback/testlab';
import {Request, HttpErrors} from '@loopback/rest';
import {ILogger} from '@sourceloop/core';
import {FileAdapterService, TenantHelperService} from '../../services';
import {
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from '../../services/proxies';
import {
  CreateTenantDTO,
  CustomerDto,
  Plan,
  Subscription,
  Tenant,
  VerifyKeyDto,
} from '../../models';
import {TenantStatus} from '@local/core';
import {PriceDto, StatusDto, SubscriptionStatus} from '@local/core';
import {CryptoHelperService} from '@sourceloop/ctrl-plane-tenant-management-service';

interface TestContact {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

interface TestAddress {
  id: string;
  street: string;
  city: string;
  state: string;
}

describe('TenantHelperService (Unit)', () => {
  let service: TenantHelperService;
  let mockRequest: Request;
  let fileAdapterServiceStub: Partial<FileAdapterService>;
  let tenantMgmtProxyServiceStub: Partial<TenantMgmtProxyService>;
  let subscriptionServiceProxyServiceStub: Partial<SubscriptionProxyService>;
  let loggerStub: ILogger;
  let cryptoHelperServiceStub: CryptoHelperService;

  const token = 'Bearer test-token';

  beforeEach(() => {
    mockRequest = {
      headers: {authorization: token},
    } as unknown as Request;

    fileAdapterServiceStub = {
      generateFileResponse: sinon.stub(),
      generateLocalStackViewUrl: sinon.stub(),
    };

    tenantMgmtProxyServiceStub = {
      verifyKey: sinon.stub(),
      createTenant: sinon.stub(),
      getTenantById: sinon.stub(),
      getAllTenantStatus: sinon.stub(),
      getTenantCount: sinon.stub(),
      getTenant: sinon.stub(),
      provisionTenant: sinon.stub(),
      findAllStatusMetrics: sinon.stub(),
    };
    cryptoHelperServiceStub = new CryptoHelperService();

    subscriptionServiceProxyServiceStub = {
      updateSubscription: sinon.stub(),
      createInvoice: sinon.stub(),
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
    };

    loggerStub = {
      error: sinon.stub(),
      info: sinon.stub(),
      debug: sinon.stub(),
      warn: sinon.stub(),
      log: sinon.stub(),
    };

    service = new TenantHelperService(
      mockRequest,
      fileAdapterServiceStub as FileAdapterService,
      tenantMgmtProxyServiceStub as TenantMgmtProxyService,
      subscriptionServiceProxyServiceStub as SubscriptionProxyService,
      cryptoHelperServiceStub,
      loggerStub,
    );
  });

  afterEach(() => sinon.restore());

  it('verifyKey should call tenantMgmtProxyService.verifyKey', async () => {
    (tenantMgmtProxyServiceStub.verifyKey as sinon.SinonStub).resolves({
      available: true,
    });

    const res = await service.verifyKey(new VerifyKeyDto({key: 'abc'}));
    sinon.assert.calledOnceWithExactly(
      tenantMgmtProxyServiceStub.verifyKey as sinon.SinonStub,
      token,
      sinon.match.has('key', 'abc'),
    );
    expect(res.available).to.be.true();
  });

  it('createTenant should call proxies correctly', async () => {
    const dto = {
      planId: 'plan-123',
      contact: {email: '<EMAIL>'},
      files: [],
      name: 'TestTenant',
      key: 'test-key',
      overallPlan: {
        planId: 'plan-id',
      },
    } as unknown as CreateTenantDTO;

    (fileAdapterServiceStub.generateFileResponse as sinon.SinonStub).resolves([
      {fileName: 'doc.pdf'},
    ]);
    (tenantMgmtProxyServiceStub.createTenant as sinon.SinonStub).resolves(
      new Tenant({id: '1', name: 'TestTenant'}),
    );
    (
      subscriptionServiceProxyServiceStub.findPlanById as sinon.SinonStub
    ).resolves(new Plan({id: 'plan-id', name: 'Test Plan'}));
    if (!subscriptionServiceProxyServiceStub.createSubscriptions) {
      subscriptionServiceProxyServiceStub.createSubscriptions = sinon.stub();
    }
    (
      subscriptionServiceProxyServiceStub.createSubscriptions as sinon.SinonStub
    ).resolves({
      id: 'sub-123',
      subscriberId: '',
      externalSubscriptionId: '',
      priceRefId: '',
      totalCost: 0,
      status: SubscriptionStatus.PENDING,
      planId: '',
    } as unknown as Subscription);
    if (!subscriptionServiceProxyServiceStub.createCustomer) {
      subscriptionServiceProxyServiceStub.createCustomer = sinon.stub();
    }
    (
      subscriptionServiceProxyServiceStub.createCustomer as sinon.SinonStub
    ).resolves({id: 'cust-123'} as unknown as CustomerDto);
    if (!subscriptionServiceProxyServiceStub.createPrice) {
      subscriptionServiceProxyServiceStub.createPrice = sinon.stub();
    }
    (
      subscriptionServiceProxyServiceStub.createPrice as sinon.SinonStub
    ).resolves({id: 'price-123'} as unknown as PriceDto);
    (tenantMgmtProxyServiceStub.provisionTenant as sinon.SinonStub).resolves();
    const res = await service.createTenant(dto);

    sinon.assert.calledOnce(
      fileAdapterServiceStub.generateFileResponse as sinon.SinonStub,
    );
    sinon.assert.calledOnce(
      tenantMgmtProxyServiceStub.createTenant as sinon.SinonStub,
    );

    expect(res).to.be.instanceOf(Tenant);
    expect(res.name).to.eql('TestTenant');
  });

  it('createTenant should throw BadRequest on invalid contact JSON', async () => {
    const badDto = {
      planId: 'p1',
      contact: '{invalid}',
      files: [],
    } as unknown as CreateTenantDTO;

    await expect(service.createTenant(badDto)).to.be.rejectedWith(
      HttpErrors.BadRequest,
    );
    sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
  });

  describe('getTenantDetails', () => {
    it('should retrieve tenant details with files and signed URLs', async () => {
      const tenantId = 'tenant-123';
      const mockTenant = {
        id: tenantId,
        name: 'Test Tenant',
        key: 'test-key',
        contacts: [
          {
            id: 'contact-1',
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
          },
        ],
        files: [
          {
            id: 'file-1',
            fileKey: '1754137174168',
            originalName: 'contract.pdf',
            source: 1,
            size: 582613,
            tenantId: tenantId,
          },
          {
            id: 'file-2',
            fileKey: '1754137174169',
            originalName: 'invoice.pdf',
            source: 1,
            size: 20700,
            tenantId: tenantId,
          },
        ],
        address: {
          id: 'address-1',
          street: '123 Main St',
          city: 'Test City',
          state: 'Test State',
        },
      };

      const expectedSignedUrl1 =
        'http://localhost:4566/bucket/file1?signed=true';
      const expectedSignedUrl2 =
        'http://localhost:4566/bucket/file2?signed=true';

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub)
        .onFirstCall()
        .resolves(expectedSignedUrl1)
        .onSecondCall()
        .resolves(expectedSignedUrl2);

      const result = await service.getTenantDetails(tenantId);

      // Verify proxy service was called correctly
      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub,
        token,
        tenantId,
      );

      // Verify file adapter service was called for each file
      sinon.assert.calledTwice(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
      );
      sinon.assert.calledWithExactly(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
        '1754137174168',
        'contract.pdf',
      );
      sinon.assert.calledWithExactly(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
        '1754137174169',
        'invoice.pdf',
      );

      // Verify logging calls
      sinon.assert.calledWith(
        loggerStub.info as sinon.SinonStub,
        'Found 2 files for tenant tenant-123',
      );
      sinon.assert.calledWith(
        loggerStub.info as sinon.SinonStub,
        'File keys found: 1754137174168, 1754137174169',
      );

      // Verify result structure
      expect(result).to.have.properties([
        'tenantDetails',
        'contact',
        'files',
        'address',
        'plan',
      ]);
      expect(result.tenantDetails).to.have.properties(['id', 'name', 'key']);
      expect(result.tenantDetails.id).to.eql(tenantId);
      expect(result.tenantDetails.name).to.eql('Test Tenant');

      // Verify contact extraction (first contact)
      expect(result.contact).to.have.properties([
        'id',
        'email',
        'firstName',
        'lastName',
      ]);
      expect((result.contact as TestContact).email).to.eql('<EMAIL>');

      // Verify files with signed URLs
      expect(Array.isArray(result.files)).to.be.true();
      expect(result.files).to.have.length(2);
      expect(result.files[0]).to.have.property('signedUrl', expectedSignedUrl1);
      expect(result.files[0]).to.have.property('originalName', 'contract.pdf');
      expect(result.files[1]).to.have.property('signedUrl', expectedSignedUrl2);
      expect(result.files[1]).to.have.property('originalName', 'invoice.pdf');

      // Verify address
      expect(result.address).to.have.properties([
        'id',
        'street',
        'city',
        'state',
      ]);
      expect((result.address as TestAddress).street).to.eql('123 Main St');

      // Verify plan is empty object
      expect(typeof result.plan).to.eql('object');
      expect(result.plan).to.be.empty();
    });

    it('should handle tenant with no contacts', async () => {
      const tenantId = 'tenant-no-contacts';
      const mockTenant = {
        id: tenantId,
        name: 'No Contacts Tenant',
        contacts: [],
        files: [],
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );

      const result = await service.getTenantDetails(tenantId);

      expect(typeof result.contact).to.eql('object');
      expect(result.contact).to.be.empty();
      expect(Array.isArray(result.files)).to.be.true();
      expect(result.files).to.be.empty();
    });

    it('should handle tenant with no files', async () => {
      const tenantId = 'tenant-no-files';
      const mockTenant = {
        id: tenantId,
        name: 'No Files Tenant',
        contacts: [{id: 'contact-1', email: '<EMAIL>'}],
        files: [],
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );

      const result = await service.getTenantDetails(tenantId);

      expect(Array.isArray(result.files)).to.be.true();
      expect(result.files).to.be.empty();
      sinon.assert.calledWith(
        loggerStub.info as sinon.SinonStub,
        'Found 0 files for tenant tenant-no-files',
      );
      sinon.assert.notCalled(
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub,
      );
    });

    it('should handle signed URL generation error gracefully', async () => {
      const tenantId = 'tenant-url-error';
      const mockTenant = {
        id: tenantId,
        name: 'URL Error Tenant',
        contacts: [],
        files: [
          {
            id: 'file-1',
            fileKey: 'error-file',
            originalName: 'error.pdf',
            source: 1,
            size: 1000,
          },
        ],
      };

      const urlError = new Error('Failed to generate signed URL');
      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (
        fileAdapterServiceStub.generateLocalStackViewUrl as sinon.SinonStub
      ).rejects(urlError);

      const result = await service.getTenantDetails(tenantId);

      // Verify error was logged
      sinon.assert.calledWith(
        loggerStub.error as sinon.SinonStub,
        'Error generating LocalStack view URL for file error.pdf:',
        urlError,
      );

      // Verify file has null signedUrl
      expect(result.files).to.have.length(1);
      expect(result.files[0]).to.have.property('signedUrl', null);
      expect(result.files[0]).to.have.property('originalName', 'error.pdf');
    });
  });

  describe('getAllStatuses', () => {
    it('should retrieve all tenant statuses', async () => {
      const mockStatuses: StatusDto[] = [
        new StatusDto({
          statuses: {
            1: 'ACTIVE',
            2: 'INACTIVE',
            3: 'SUSPENDED',
          },
        }),
      ];

      (
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub
      ).resolves(mockStatuses);

      const result = await service.getAllStatuses();

      // Verify proxy service was called correctly
      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub,
        token,
      );

      // Verify result
      expect(Array.isArray(result)).to.be.true();
      expect(result).to.have.length(1);
      expect(result).to.eql(mockStatuses);
      expect(result[0]).to.have.property('statuses');
      expect(result[0].statuses).to.have.property('1', 'ACTIVE');
      expect(result[0].statuses).to.have.property('2', 'INACTIVE');
      expect(result[0].statuses).to.have.property('3', 'SUSPENDED');
    });

    it('should handle empty status list', async () => {
      (
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub
      ).resolves([]);

      const result = await service.getAllStatuses();

      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub,
        token,
      );

      expect(Array.isArray(result)).to.be.true();
      expect(result).to.be.empty();
    });

    it('should propagate errors from proxy service', async () => {
      const proxyError = new Error('Proxy service error');
      (
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub
      ).rejects(proxyError);

      await expect(service.getAllStatuses()).to.be.rejectedWith(
        'Proxy service error',
      );

      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getAllTenantStatus as sinon.SinonStub,
        token,
      );
    });
    it('getDashboardMetrics should return status and tenants arrays', async () => {
      const dummyToken = 'dummy-temp-token';
      const mockStatus = [{count: 5, status: 'ACTIVE'}];
      const mockTenants = [
        new Tenant({
          id: '1',
          name: 'Tenant1',
          status: TenantStatus.ACTIVE,
          key: 'key1',
          domains: ['tenant1.com'],
          contacts: [],
          resources: [],
          addressId: 'address1',
          lang: 'en',
          createdBy: 'user1',
          createdOn: new Date(),
          files: [],
          getId: () => '1',
          getIdObject: () => ({id: '1'}),
          toJSON: function () {
            return this;
          },
          toObject: () => ({}),
        }),
      ];

      sinon
        .stub(cryptoHelperServiceStub, 'generateTempToken')
        .returns(dummyToken);
      (
        tenantMgmtProxyServiceStub.findAllStatusMetrics as sinon.SinonStub
      ).resolves(mockStatus);
      (tenantMgmtProxyServiceStub.getTenant as sinon.SinonStub).resolves(
        mockTenants,
      );

      const result = await service.getDashboardMetrics();

      expect(result).to.have.properties(['status', 'tenants']);
      expect(result.status).to.eql(mockStatus);
      expect(result.tenants).to.eql(mockTenants);
    });
  });
});
