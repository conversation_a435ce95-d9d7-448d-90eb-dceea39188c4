import {expect, sinon} from '@loopback/testlab';
import {Request, HttpErrors} from '@loopback/rest';
import {ILogger} from '@sourceloop/core';
import {CryptoHelperService as LocalCryptoService} from '@local/core';
import {SSMClient, GetParameterCommand} from '@aws-sdk/client-ssm';
import axios from 'axios';
import {ApplicationWebhookService} from '../../services/application-webhook.service';
import {
  TenantMgmtProxyService,
  SubscriptionProxyService,
} from '../../services/proxies';
import {EditTenantDTO, Contact} from '../../models';

describe('ApplicationWebhookService (Unit)', () => {
  let service: ApplicationWebhookService;
  let mockRequest: Request;
  let tenantMgmtProxyServiceStub: Partial<TenantMgmtProxyService>;
  let subscriptionProxyServiceStub: Partial<SubscriptionProxyService>;
  let loggerStub: ILogger;
  let localCryptoServiceStub: Partial<LocalCryptoService>;
  let axiosStub: sinon.SinonStub;
  let ssmClientStub: sinon.SinonStub;

  const token = 'Bearer test-token';
  const tenantId = 'tenant-123';
  const mockSecret = 'mock-secret-value';
  const mockSignature = 'mock-signature';

  beforeEach(() => {
    // Setup request with authorization header
    mockRequest = {
      headers: {authorization: token},
    } as unknown as Request;

    // Setup proxy service stubs
    tenantMgmtProxyServiceStub = {
      getTenantById: sinon.stub(),
    };

    subscriptionProxyServiceStub = {
      findPlanById: sinon.stub(),
    };

    // Setup logger stub
    loggerStub = {
      error: sinon.stub(),
      info: sinon.stub(),
      debug: sinon.stub(),
      warn: sinon.stub(),
      log: sinon.stub(),
    };

    // Setup crypto service stub
    localCryptoServiceStub = {
      generateHmacSHA256: sinon.stub().returns(mockSignature),
      generateComplexRandomString: sinon.stub().returns('random-password-123'),
    };

    // Setup axios stub
    axiosStub = sinon.stub(axios, 'post');

    // Setup SSM client stub
    ssmClientStub = sinon.stub(SSMClient.prototype, 'send');

    // Set environment variables
    process.env.SUBDOMAIN_URL = 'example.com';
    process.env.AWS_REGION = 'us-east-1';
    process.env.ENVIRONMENT = 'test';

    service = new ApplicationWebhookService(
      mockRequest,
      tenantMgmtProxyServiceStub as TenantMgmtProxyService,
      subscriptionProxyServiceStub as SubscriptionProxyService,
      loggerStub,
      localCryptoServiceStub as LocalCryptoService,
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('constructor', () => {
    it('should initialize with authorization token', () => {
      expect(service.token).to.equal(token);
    });

    it('should throw Unauthorized error when no authorization header', () => {
      const requestWithoutAuth = {
        headers: {},
      } as unknown as Request;

      expect(
        () =>
          new ApplicationWebhookService(
            requestWithoutAuth,
            tenantMgmtProxyServiceStub as TenantMgmtProxyService,
            subscriptionProxyServiceStub as SubscriptionProxyService,
            loggerStub,
            localCryptoServiceStub as LocalCryptoService,
          ),
      ).to.throw(HttpErrors.Unauthorized);
    });
  });

  describe('getSSMCredentials', () => {
    it('should successfully retrieve secret from SSM', async () => {
      const mockResponse = {
        Parameter: {
          Value: mockSecret,
        },
      };

      ssmClientStub.resolves(mockResponse);

      const result = await service.getSSMCredentials('tenant-key', 'dev');

      expect(result).to.equal(mockSecret);
      sinon.assert.calledOnce(ssmClientStub);
      sinon.assert.calledWith(
        ssmClientStub,
        sinon.match.instanceOf(GetParameterCommand),
      );
    });

    it('should throw InternalServerError when SSM call fails', async () => {
      const ssmError = new Error('SSM service unavailable');
      ssmClientStub.rejects(ssmError);

      await expect(
        service.getSSMCredentials('tenant-key', 'dev'),
      ).to.be.rejectedWith(HttpErrors.InternalServerError);

      sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
      sinon.assert.calledWith(
        loggerStub.error as sinon.SinonStub,
        `Error fetching SSM secret: ${ssmError}`,
      );
    });

    it('should handle missing parameter value', async () => {
      const mockResponse = {
        Parameter: {},
      };

      ssmClientStub.resolves(mockResponse);

      const result = await service.getSSMCredentials('tenant-key', 'dev');

      expect(result).to.be.undefined();
    });
  });

  describe('checkUserAvailability', () => {
    const mockKey = 'tenant-key';
    const mockEmail = '<EMAIL>';
    const mockUserName = 'testuser';

    it('should successfully check user availability', async () => {
      const mockResponse = {
        data: {available: true},
      };

      axiosStub.resolves(mockResponse);

      const result = await service.checkUserAvailability(
        mockKey,
        mockEmail,
        mockUserName,
        mockSignature,
      );

      expect(result).to.eql({available: true});

      sinon.assert.calledOnce(axiosStub);
      sinon.assert.calledWith(
        axiosStub,
        `https://${mockKey}.${process.env.SUBDOMAIN_URL}/api/user-management/webhook/check-tenant-admin-availability`,
        {
          email: mockEmail,
          username: mockUserName,
        },
        {
          headers: {
            'x-api-key': mockSignature,
            'Content-Type': 'application/json',
          },
        },
      );

      sinon.assert.calledOnce(loggerStub.info as sinon.SinonStub);
    });

    it('should handle axios error and throw custom error', async () => {
      const axiosError = {
        response: {
          status: 400,
          data: {
            message: 'User not available',
            data: {details: 'Email already exists'},
          },
        },
      };

      axiosStub.rejects(axiosError);

      try {
        await service.checkUserAvailability(
          mockKey,
          mockEmail,
          mockUserName,
          mockSignature,
        );
        throw new Error('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('User not available');
        expect((error as Error & {statusCode: number}).statusCode).to.equal(
          400,
        );
        expect((error as Error & {details: unknown}).details).to.eql({
          details: 'Email already exists',
        });
      }
    });

    it('should handle axios error without response data', async () => {
      const axiosError = {
        response: {
          status: 500,
        },
      };

      axiosStub.rejects(axiosError);

      try {
        await service.checkUserAvailability(
          mockKey,
          mockEmail,
          mockUserName,
          mockSignature,
        );
        throw new Error('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Request failed');
        expect((error as Error & {statusCode: number}).statusCode).to.equal(
          500,
        );
      }
    });
  });

  describe('validateUser', () => {
    const mockEmail = '<EMAIL>';
    const mockUserName = 'testuser';

    beforeEach(() => {
      // Setup default stubs for validateUser dependencies
      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub) =
        sinon.stub();
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub) =
        sinon.stub();
      sinon.stub(service, 'getSSMCredentials').resolves(mockSecret);
      sinon.stub(service, 'checkUserAvailability').resolves({available: true});
    });

    it('should throw BadRequest when neither email nor username provided', async () => {
      await expect(service.validateUser('', '', tenantId)).to.be.rejectedWith(
        HttpErrors.BadRequest,
      );
    });

    it('should throw NotFound when tenant not found', async () => {
      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        null,
      );

      await expect(
        service.validateUser(mockEmail, mockUserName, tenantId),
      ).to.be.rejectedWith(HttpErrors.NotFound);
    });

    it('should throw NotFound when plan not found', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        null,
      );

      await expect(
        service.validateUser(mockEmail, mockUserName, tenantId),
      ).to.be.rejectedWith(HttpErrors.NotFound);
    });

    it('should throw NotFound when secret not found', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );
      (service.getSSMCredentials as sinon.SinonStub).resolves(null);

      await expect(
        service.validateUser(mockEmail, mockUserName, tenantId),
      ).to.be.rejectedWith(HttpErrors.NotFound);
    });

    it('should successfully validate user', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };
      const expectedResult = {available: true};

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );

      const result = await service.validateUser(
        mockEmail,
        mockUserName,
        tenantId,
      );

      expect(result).to.eql(expectedResult);

      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub,
        token,
        tenantId,
      );
      sinon.assert.calledOnceWithExactly(
        subscriptionProxyServiceStub.findPlanById as sinon.SinonStub,
        token,
        'plan-123',
      );
      sinon.assert.calledOnceWithExactly(
        service.getSSMCredentials as sinon.SinonStub,
        'tenant-key',
        'premium',
      );
      sinon.assert.calledOnceWithExactly(
        localCryptoServiceStub.generateHmacSHA256 as sinon.SinonStub,
        mockSecret,
        tenantId,
      );
      sinon.assert.calledOnceWithExactly(
        service.checkUserAvailability as sinon.SinonStub,
        'tenant-key',
        mockEmail,
        mockUserName,
        mockSignature,
      );
    });
  });

  describe('updateContact', () => {
    const mockDto = new EditTenantDTO({
      contact: {
        email: '<EMAIL>',
        userName: 'newuser',
        firstName: 'New',
        lastName: 'User',
        phoneNumber: '1234567890',
        designation: 'Manager',
        isPrimary: true,
        countryCode: 'US',
      } as Omit<Contact, 'id' | 'tenantId'>,
      totalCost: 1000,
      planId: 'plan-123',
      existingFiles: [],
      files: [],
    });

    beforeEach(() => {
      // Setup default stubs
      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub) =
        sinon.stub();
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub) =
        sinon.stub();
      sinon.stub(service, 'getSSMCredentials').resolves(mockSecret);
    });

    it('should return early when contact email and username are unchanged', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
        contacts: [
          {
            email: '<EMAIL>',
            userName: 'newuser',
          },
        ],
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );

      const result = await service.updateContact(tenantId, mockDto);

      expect(result).to.be.undefined();
      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub,
        token,
        tenantId,
        {
          include: [
            {
              relation: 'contacts',
            },
          ],
        },
      );
      sinon.assert.notCalled(
        subscriptionProxyServiceStub.findPlanById as sinon.SinonStub,
      );
    });

    it('should throw NotFound when secret not found', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
        contacts: [
          {
            email: '<EMAIL>',
            userName: 'olduser',
          },
        ],
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );
      (service.getSSMCredentials as sinon.SinonStub).resolves(null);

      await expect(service.updateContact(tenantId, mockDto)).to.be.rejectedWith(
        HttpErrors.NotFound,
      );
    });

    it('should return null when availability check fails', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
        contacts: [
          {
            email: '<EMAIL>',
            userName: 'olduser',
          },
        ],
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );

      // Mock availability check to fail (non-200 status)
      axiosStub.resolves({status: 400});

      const result = await service.updateContact(tenantId, mockDto);

      expect(result).to.be.null();
      sinon.assert.calledOnce(axiosStub);
    });

    it('should return null when admin creation fails', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
        contacts: [
          {
            email: '<EMAIL>',
            userName: 'olduser',
          },
        ],
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );

      // Mock availability check to succeed
      axiosStub.onFirstCall().resolves({status: 200});
      // Mock admin creation to fail
      axiosStub.onSecondCall().resolves({status: 500});

      const result = await service.updateContact(tenantId, mockDto);

      expect(result).to.be.null();
      sinon.assert.calledTwice(axiosStub);
    });

    it('should successfully update contact and return random password', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
        contacts: [
          {
            email: '<EMAIL>',
            userName: 'olduser',
          },
        ],
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };
      const expectedPassword = 'random-password-123';

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );

      // Mock availability check to succeed
      axiosStub.onFirstCall().resolves({status: 200});
      // Mock admin creation to succeed
      axiosStub.onSecondCall().resolves({
        status: 200,
        data: {success: true},
      });

      const result = await service.updateContact(tenantId, mockDto);

      expect(result).to.equal(expectedPassword);

      // Verify all service calls
      sinon.assert.calledOnceWithExactly(
        tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub,
        token,
        tenantId,
        {
          include: [
            {
              relation: 'contacts',
            },
          ],
        },
      );

      sinon.assert.calledOnceWithExactly(
        subscriptionProxyServiceStub.findPlanById as sinon.SinonStub,
        token,
        'plan-123',
      );

      sinon.assert.calledOnceWithExactly(
        service.getSSMCredentials as sinon.SinonStub,
        'tenant-key',
        'premium',
      );

      sinon.assert.calledOnceWithExactly(
        localCryptoServiceStub.generateHmacSHA256 as sinon.SinonStub,
        mockSecret,
        tenantId,
      );

      sinon.assert.calledOnceWithExactly(
        localCryptoServiceStub.generateComplexRandomString as sinon.SinonStub,
        15,
      );

      // Verify axios calls
      sinon.assert.calledTwice(axiosStub);

      // Verify availability check call
      sinon.assert.calledWith(
        axiosStub.firstCall,
        `https://tenant-key.${process.env.SUBDOMAIN_URL}/api/user-management/webhook/check-tenant-admin-availability`,
        {
          email: '<EMAIL>',
          username: 'newuser',
        },
        {
          headers: {
            'x-api-key': mockSignature,
            'Content-Type': 'application/json',
          },
        },
      );

      // Verify admin creation call
      sinon.assert.calledWith(
        axiosStub.secondCall,
        `https://tenant-key.${process.env.SUBDOMAIN_URL}/api/user-management/webhook/change-tenant-admin`,
        {
          email: '<EMAIL>',
          username: 'newuser',
          password: expectedPassword,
        },
        {
          headers: {
            'x-api-key': mockSignature,
            'Content-Type': 'application/json',
          },
        },
      );

      // Verify logging
      sinon.assert.calledWith(
        loggerStub.info as sinon.SinonStub,
        'Tenant admin created: {"success":true}',
      );
    });

    it('should handle axios errors gracefully in availability check', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
        contacts: [
          {
            email: '<EMAIL>',
            userName: 'olduser',
          },
        ],
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );

      // Mock axios to throw error
      const axiosError = new Error('Network error') as Error & {
        response: {status: number; data: string};
      };
      axiosError.response = {status: 500, data: 'Server error'};
      axiosStub.onFirstCall().rejects(axiosError);

      const result = await service.updateContact(tenantId, mockDto);

      expect(result).to.be.null();
      sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
      sinon.assert.calledWith(
        loggerStub.error as sinon.SinonStub,
        'Error checking user availability: Error: Network error',
      );
    });

    it('should handle axios errors gracefully in admin creation', async () => {
      const mockTenant = {
        id: tenantId,
        key: 'tenant-key',
        planId: 'plan-123',
        contacts: [
          {
            email: '<EMAIL>',
            userName: 'olduser',
          },
        ],
      };
      const mockPlan = {
        id: 'plan-123',
        tier: 'premium',
      };

      (tenantMgmtProxyServiceStub.getTenantById as sinon.SinonStub).resolves(
        mockTenant,
      );
      (subscriptionProxyServiceStub.findPlanById as sinon.SinonStub).resolves(
        mockPlan,
      );

      // Mock availability check to succeed
      axiosStub.onFirstCall().resolves({status: 200});

      // Mock admin creation to throw error
      const axiosError = new Error('Creation failed') as Error & {
        response: {status: number; data: string};
      };
      axiosError.response = {status: 400, data: 'Bad request'};
      axiosStub.onSecondCall().rejects(axiosError);

      const result = await service.updateContact(tenantId, mockDto);

      expect(result).to.be.null();
      sinon.assert.calledOnce(loggerStub.error as sinon.SinonStub);
      sinon.assert.calledWith(
        loggerStub.error as sinon.SinonStub,
        'Error creating tenant admin: Bad request',
      );
    });
  });
});
