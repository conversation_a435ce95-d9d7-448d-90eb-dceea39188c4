import {CryptoHelperService as LocalCryptoService} from '@local/core';
import {inject, service} from '@loopback/core';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {ILogger, LOGGER} from '@sourceloop/core';
import {SSMClient, GetParameterCommand} from '@aws-sdk/client-ssm';
import {EditTenantDTO} from '../models';
import {SubscriptionProxyService, TenantMgmtProxyService} from './proxies';
import axios from 'axios';

/**
 * Service responsible for handling webhook operations related to tenant management.
 *
 * @remarks
 * This service interacts with tenant and subscription proxy services, manages authentication tokens,
 * and communicates with tenant-specific user management APIs via webhooks. It also handles secure
 * credential retrieval from AWS SSM and cryptographic operations for request signing.
 *
 * @constructor
 * @param request - The HTTP request object, used to extract the authorization token.
 * @param tenantMgmtProxyService - Service for tenant management proxy operations.
 * @param subscriptionProxyService - Service for subscription management proxy operations.
 * @param logger - Logger instance for logging errors and information.
 * @param localCryptoService - Service for cryptographic operations such as HMAC generation.
 *
 * @throws {HttpErrors.Unauthorized} If the authorization header is missing in the request.
 */
export class ApplicationWebhookService {
  token: string;
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
    @service(LocalCryptoService)
    private readonly localCryptoService: LocalCryptoService,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    } else {
      throw new HttpErrors.Unauthorized();
    }
  }

  /**
   * Updates the primary contact information for a given tenant.
   *
   * This method checks if the contact's email and username have changed. If they have,
   * it verifies the availability of the new admin credentials via a webhook, and if available,
   * updates the tenant admin by sending a request to the tenant's user management API.
   * Generates a new random password for the updated admin user.
   *
   * @param tenantId - The unique identifier of the tenant whose contact is being updated.
   * @param dto - The data transfer object containing the updated tenant contact information.
   * @returns A promise that resolves to the new random password if the update is successful, or `null` if not updated.
   * @throws {HttpErrors.NotFound} If the secret for the tenant cannot be found.
   */
  async updateContact(tenantId: string, dto: EditTenantDTO) {
    const tenantData = await this.tenantMgmtProxyService.getTenantById(
      this.token,
      tenantId,
      {
        include: [
          {
            relation: 'contacts',
          },
        ],
      },
    );

    if (
      tenantData.contacts[0].email === dto.contact.email &&
      tenantData.contacts[0].userName === dto.contact.userName
    ) {
      return;
    }

    const planDetails = await this.subscriptionProxyService.findPlanById(
      this.token,
      tenantData.planId ?? '',
    );

    const tier = planDetails.tier;

    const payload = {
      email: dto.contact.email,
      username: dto.contact.userName,
    };

    const secret = await this.getSSMCredentials(tenantData.key ?? '', tier);
    if (!secret) {
      throw new HttpErrors.NotFound(`Secret for tenant ${tenantId} not found`);
    }

    const signature = this.localCryptoService.generateHmacSHA256(
      secret,
      tenantId,
    );

    const checkUrl = `https://${tenantData.key}.${process.env.SUBDOMAIN_URL}/api/user-management/webhook/check-tenant-admin-availability`;
    const checkAvailRes = await axios
      .post(checkUrl, payload, {
        headers: {
          'x-api-key': signature,
          'Content-Type': 'application/json',
        },
      })
      .catch(err => {
        this.logger.error(`Error checking user availability: ${err}`);
        return err.response;
      });

    const successCode = 200;
    if (checkAvailRes?.status !== successCode) {
      return null;
    }

    const pwdLen = 15;
    const randomPassword =
      this.localCryptoService.generateComplexRandomString(pwdLen);

    const userData = {
      ...payload,
      password: randomPassword,
    };

    const url = `https://${tenantData.key}.${process.env.SUBDOMAIN_URL}/api/user-management/webhook/change-tenant-admin`;
    const axiosRes = await axios
      .post(url, userData, {
        headers: {
          'x-api-key': signature,
          'Content-Type': 'application/json',
        },
      })
      .catch(err => {
        this.logger.error(
          `Error creating tenant admin: ${err?.response?.data}`,
        );
        return err.response;
      });

    this.logger.info(`Tenant admin created: ${JSON.stringify(axiosRes.data)}`);
    if (axiosRes?.status !== successCode) {
      return null;
    }
    return randomPassword;
  }

  /**
   * Retrieves a secret value from AWS Systems Manager (SSM) Parameter Store for a given tenant and tier.
   *
   * @param tenantKeyParam - The unique key identifying the tenant.
   * @param tier - The environment tier (e.g., 'dev', 'prod') for which to fetch the secret.
   * @returns A promise that resolves to the secret value as a string, or throws an error if retrieval fails.
   * @throws {HttpErrors.InternalServerError} If the secret cannot be fetched from SSM.
   */
  async getSSMCredentials(tenantKeyParam: string, tier: string) {
    const client = new SSMClient({
      region: process.env.AWS_REGION,
    });

    const params = {
      Name: `/distek-saas/${process.env.ENVIRONMENT}/${tier}/${tenantKeyParam}/secret`,
      WithDecryption: true,
    };

    try {
      const command = new GetParameterCommand(params);
      const response = await client.send(command);
      return response.Parameter?.Value;
    } catch (error) {
      this.logger.error(`Error fetching SSM secret: ${error}`);
      throw new HttpErrors.InternalServerError('Failed to fetch SSM secret');
    }
  }

  /**
   * Validates the existence and availability of a user within a specific tenant context.
   *
   * This method checks if either an email or username is provided, retrieves the tenant and its subscription plan,
   * fetches the appropriate secret for the tenant and plan tier, generates a signature, and finally checks user availability.
   * Throws appropriate HTTP errors if any required entity is not found or if input validation fails.
   *
   * @param email - The email address of the user to validate.
   * @param userName - The username of the user to validate.
   * @param tenantId - The unique identifier of the tenant.
   * @returns A promise resolving to the result of the user availability check.
   * @throws {HttpErrors.BadRequest} If neither email nor username is provided.
   * @throws {HttpErrors.NotFound} If the tenant, plan, or secret cannot be found.
   */
  async validateUser(email: string, userName: string, tenantId: string) {
    if (!email && !userName) {
      throw new HttpErrors.BadRequest('Email or username are required');
    }

    const tenant = await this.tenantMgmtProxyService.getTenantById(
      this.token,
      tenantId,
    );

    if (!tenant) {
      throw new HttpErrors.NotFound(`Tenant with ID ${tenantId} not found`);
    }

    const planDetails = await this.subscriptionProxyService.findPlanById(
      this.token,
      tenant.planId ?? '',
    );

    if (!planDetails) {
      throw new HttpErrors.NotFound(`Plan for tenant ${tenantId} not found`);
    }

    const tier = planDetails.tier;

    const secret = await this.getSSMCredentials(tenant.key ?? '', tier);
    if (!secret) {
      throw new HttpErrors.NotFound(`Secret for tenant ${tenantId} not found`);
    }

    const signature = this.localCryptoService.generateHmacSHA256(
      secret,
      tenantId,
    );

    return this.checkUserAvailability(
      tenant.key ?? '',
      email,
      userName,
      signature,
    );
  }
  /**
   * Checks the availability of a user (by email and username) for a tenant admin via a webhook.
   *
   * Sends a POST request to the tenant's user management webhook endpoint to verify if the specified
   * email and username are available for registration as a tenant admin.
   *
   * @param key - The tenant key used to construct the subdomain URL.
   * @param email - The email address to check for availability.
   * @param userName - The username to check for availability.
   * @param signature - The API key signature for authentication in the request headers.
   * @returns A promise that resolves with the response data from the webhook indicating user availability.
   * @throws An error if the HTTP request fails, including status code and error details if available.
   */
  async checkUserAvailability(
    key: string,
    email: string,
    userName: string,
    signature: string,
  ) {
    const payload = {
      email,
      username: userName,
    };

    const url = `https://${key}.${process.env.SUBDOMAIN_URL}/api/user-management/webhook/check-tenant-admin-availability`;

    const axiosRes = await axios
      .post(url, payload, {
        headers: {
          'x-api-key': signature,
          'Content-Type': 'application/json',
        },
      })
      .catch(err => {
        console.log({err}); // NOSONAR
        // @es-lint-disable-next-line
        const error = new Error(
          err?.response?.data?.message || 'Request failed',
        );
        Object.assign(error, {
          statusCode: err?.response?.status,
          details: err?.response?.data?.data,
        });
        throw error;
      });

    this.logger.info('User availability check response', axiosRes.data);
    return axiosRes.data;
  }
}
