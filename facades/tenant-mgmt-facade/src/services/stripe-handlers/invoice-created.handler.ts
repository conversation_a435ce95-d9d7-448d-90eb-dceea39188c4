import {
  CryptoHelperService,
  getCCEmailsForEvent,
  InvoiceResponse,
  Notification,
  NotificationEventType,
  NotificationType,
  PermissionKey,
  StripeEventResponse,
  StripeEvents,
  TemplateService,
} from '@local/core';
import {HttpErrors} from '@loopback/rest';
import {NotificationProxyService, TenantMgmtProxyService} from '../proxies';
import {IStripeEventHandler} from './stripe-event-handler.interface';

export class InvoiceCreatedHandler implements IStripeEventHandler {
  constructor(
    private readonly notificationProxyService: NotificationProxyService,
    private readonly cryptoHelperService: CryptoHelperService,
    private readonly templateService: TemplateService,
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,
  ) {}

  canHandle(event: StripeEventResponse<InvoiceResponse | undefined>): boolean {
    return (
      event.event === StripeEvents.INVOICE_CREATED &&
      !!event.success &&
      !!event.sendEmail
    );
  }

  async handle(
    event: StripeEventResponse<InvoiceResponse | undefined>,
  ): Promise<void> {
    const token = this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.SYSTEM_USER_TENANT_ID,
      tenantId: process.env.SYSTEM_USER_TENANT_ID,
      defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
      permissions: [
        PermissionKey.CreateNotification,
        PermissionKey.ViewNotificationTemplate,
        PermissionKey.ViewTenant,
      ],
    });

    const tenant = await this.tenantMgmtProxyService.getTenantById(
      `Bearer ${token}`,
      event.userId ?? '',
    );

    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.InvoicePaymentRequest,
      NotificationType.EMAIL,
      token,
    );

    if (!template) {
      throw new HttpErrors.InternalServerError(
        'Notification template for forget password not found',
      );
    }

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dueDate = tomorrow.toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });

    const emailBody = this.templateService.generateEmail(template.body, {
      DISTEK_LOGO: process.env.DISTEK_LOGO ?? '',
      USER_NAME: tenant.name ?? '',
      PLAN_NAME: tenant.planName ?? '',
      INVOICE_AMOUNT: `$${event.amount}`,
      DUE_DATE: dueDate,
      PAYMENT_LINK: event.message ?? '',
      SUPPORT_EMAIL: process.env.SUPPORT_EMAIL ?? '',
      HREF_SUPPORT: `mailto:${process.env.SUPPORT_EMAIL}`,
      BEST_REGARDS_BY: process.env.BEST_REGARDS_BY ?? '',
    });
    const ccEmails = getCCEmailsForEvent(template.eventName);
    const notification: Notification = new Notification({
      subject: template.subject,
      body: emailBody,
      receiver: {
        to: [
          {
            id: event.userId ?? '',
            toEmail: event.email,
            cc: ccEmails,
          },
        ],
      },
      type: 1,
      sentDate: new Date(),
      options: {
        fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
      },
    });

    await this.notificationProxyService.createNotification(token, notification);
  }
}
