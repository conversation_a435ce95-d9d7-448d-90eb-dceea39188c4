import {
  Per<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  StatusDto,
  SubscriptionStatus,
} from '@local/core';
import {BindingScope, inject, injectable} from '@loopback/core';
import {Count, Filter, Where} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {<PERSON><PERSON><PERSON>, LOGGER} from '@sourceloop/core';
import {
  CreateTenantDTO,
  CustomerDto,
  DashboardMetricsDto,
  EditTenantDTO,
  FileObject,
  Plan,
  Tenant,
  TenantOnboardDTO,
  UpdateTenantDto,
  VerifyKeyDto,
} from '../models';
import {FileMetadata} from '../types';
import {FileAdapterService} from './file-adapter.service';
import {SubscriptionProxyService, TenantMgmtProxyService} from './proxies';
import {
  CryptoHelperService,
  IPlan,
  ISubscription,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {ChargeDto} from '@sourceloop/ctrl-plane-subscription-service';
const DEFAULT_TRIAL_DAYS = 14;

const TEMP_TOKEN_EXPIRATION = 30000; // 30 seconds
/**
 * Returns the number of trial days from the selected plan metadata.
 * Defaults to DEFAULT_TRIAL_DAYS if not specified.
 *
 * @param {Plan} selectedPlan The selected plan object
 * @returns {number} Number of trial days
 */
function getTrialDays(selectedPlan: Plan) {
  return (
    (selectedPlan.metaData &&
      (selectedPlan.metaData as {trialDays?: number}).trialDays) ??
    DEFAULT_TRIAL_DAYS
  );
}

/**
 * Returns the primary contact of a tenant if available.
 * Returns an empty object if no contacts are present.
 *
 * @param {Tenant} tenant The tenant object
 * @returns {object} Contact object or empty object
 */
function getContact(tenant: Tenant) {
  return tenant.contacts && tenant.contacts.length > 0
    ? tenant.contacts[0]
    : {};
}

@injectable({scope: BindingScope.TRANSIENT})
/**
 * A helper service to manage tenant onboarding operations such as key verification,
 * tenant creation, and file handling.
 */
export class TenantHelperService {
  token: string;

  /**
   * Constructs a new instance of the TenantHelperService.
   *
   * @param request - The current HTTP request, used to extract the authorization token.
   * @param fileAdapterService - Service to manage file operations.
   * @param subscriptionProxyService - Proxy service to interact with the subscription service.
   * @param tenantMgmtProxyService - Proxy service to interact with the tenant management service.
   * @param logger - Logger for logging errors or messages.
   *
   * @throws {HttpErrors.Unauthorized} If the Authorization header is missing.
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.FileAdapterService')
    private readonly fileAdapterService: FileAdapterService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,
    @inject('services.CryptoHelperService')
    private readonly cryptoHelperService: CryptoHelperService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    } else {
      throw new HttpErrors.Unauthorized();
    }
  }

  /**
   * Verifies if a given key is valid for tenant onboarding.
   *
   * @param body - The DTO containing key details to verify.
   * @returns A promise resolving to the result of the key verification.
   */
  async verifyKey(body: VerifyKeyDto) {
    return this.tenantMgmtProxyService.verifyKey(this.token, body);
  }

  /**
   * Verifies and normalizes the tenant payload for create or edit operations.
   *
   * - Parses the `contact` property if it's a JSON string.
   * - Ensures invalid JSON throws a `BadRequest` error.
   * - Cleans up empty `phoneNumber` and `designation` fields by setting them to `undefined`.
   *
   * @private
   * @param {CreateTenantDTO | EditTenantDTO} dto - The tenant DTO to validate and normalize.
   * @throws {HttpErrors.BadRequest} If the `contact` field is not a valid JSON string.
   */
  private _verifyPayload(dto: CreateTenantDTO | EditTenantDTO) {
    if (typeof dto.contact === 'string') {
      try {
        dto.contact = JSON.parse(dto.contact);
      } catch (error) {
        this.logger.error(JSON.stringify(error));
        throw new HttpErrors.BadRequest('Invalid JSON format for contact');
      }
    }

    if (dto.contact) {
      if (dto.contact.phoneNumber === '') {
        dto.contact.phoneNumber = undefined;
      }

      if (dto.contact.designation === '') {
        dto.contact.designation = undefined;
      }
    }
  }

  /**
   * Parses and validates the `existingFiles` field in the tenant DTO.
   *
   * - If `existingFiles` is a JSON string, it is parsed into a `FileObject[]`.
   * - Logs and throws a `BadRequest` error for invalid JSON.
   *
   * @private
   * @param {EditTenantDTO} dto - The tenant DTO containing `existingFiles`.
   * @returns {FileObject[]} The parsed list of file objects.
   * @throws {HttpErrors.BadRequest} If the `existingFiles` field is not valid JSON.
   */
  private parseExistingFiles(dto: EditTenantDTO): FileObject[] {
    if (typeof dto.existingFiles === 'string') {
      try {
        return JSON.parse(dto.existingFiles);
      } catch (error) {
        this.logger.error(JSON.stringify(error));
        throw new HttpErrors.BadRequest(
          'Invalid JSON format for existing files',
        );
      }
    }
    return dto.existingFiles;
  }

  /**
   * Updates an existing tenant with new details.
   *
   * - Verifies and normalizes the DTO payload.
   * - Parses existing files and generates new file responses if provided.
   * - Fetches the selected subscription plan and validates its existence.
   * - Updates the tenant record via the tenant management proxy service.
   * - Validates the existing active subscription for the tenant.
   * - If plan or cost has changed, updates the subscription with new pricing details.
   *
   * @async
   * @param {EditTenantDTO} dto - The tenant details to be updated.
   * @param {string} tenantId - The unique identifier of the tenant to update.
   * @returns {Promise<void>} A promise that resolves when the tenant update is complete.
   *
   * @throws {Error} If the selected plan does not exist.
   * @throws {Error} If no active subscription exists for the tenant.
   * @throws {HttpErrors.BadRequest} If file or contact fields contain invalid JSON.
   */
  async editTenant(dto: EditTenantDTO, tenantId: string) {
    this._verifyPayload(dto);
    dto.existingFiles = this.parseExistingFiles(dto);

    const selectedPlan = await this.subscriptionProxyService.findPlanById(
      this.token,
      dto.planId,
      {
        include: [
          {
            relation: 'currency',
          },
          {
            relation: 'billingCycle',
          },
        ],
      },
    );
    if (!selectedPlan) {
      throw new Error('selected plan does not exist');
    }

    const {files, existingFiles, numberOfUsers, totalCost, planId, ...rest} =
      dto;
    const fileRes: FileObject[] | undefined =
      await this.fileAdapterService.generateFileResponse(
        files ? (files as unknown as FileMetadata | FileMetadata[]) : [],
      );

    const allFiles = [
      ...fileRes,
      ...existingFiles.map(file => ({
        fileKey: file.fileKey,
        originalName: file.originalName,
        source: file.source,
        size: file.size,
      })),
    ];

    const newDto = new UpdateTenantDto({
      ...rest,
      selectedFiles: allFiles,
      planName: selectedPlan.name,
      planId: selectedPlan.id,
    });

    await this.tenantMgmtProxyService.updateTenantById(
      this.token,
      tenantId,
      newDto,
    );

    const subscription = await this.subscriptionProxyService.getSubscriptions(
      this.token,
      JSON.stringify({
        where: {
          subscriberId: tenantId,
          status: {
            inq: [
              SubscriptionStatus.ACTIVE,
              SubscriptionStatus.PENDING,
              SubscriptionStatus.TRIAL,
              SubscriptionStatus.TRIAL_REMINDER_SENT,
              SubscriptionStatus.TRIAL_SUSPEND,
            ],
          },
        },
        include: [
          {
            relation: 'plan',
            scope: {
              include: [
                {
                  relation: 'currency',
                },
                {
                  relation: 'billingCycle',
                },
              ],
            },
          },
        ],
      }),
    );
    if (subscription.length <= 0) {
      throw new Error('subscription does not exist');
    }

    if (
      (dto.planId && dto.planId !== subscription[0].planId) ||
      (dto.totalCost && dto.totalCost !== subscription[0].totalCost)
    ) {
      const price = await this.subscriptionProxyService.createPrice(
        this.token,
        {
          product: selectedPlan.productRefId,
          recurring: {
            interval: selectedPlan.billingCycle?.durationUnit,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            interval_count: selectedPlan.billingCycle?.duration,
          },
          currency: selectedPlan.currency?.currencyCode.toLowerCase(),
          active: true,
          //eslint-disable-next-line @typescript-eslint/naming-convention
          unit_amount: Math.round(dto.totalCost * 100), // Convert to cents
        },
      );

      await this.subscriptionProxyService.updateSubscriptionById(
        this.token,
        subscription[0].id,
        {
          priceRefId: price.id,
          totalCost: Number(totalCost),
          numberOfUsers: numberOfUsers ? Number(numberOfUsers) : undefined,
          planId,
          prorationBehavior: ProrationBehaviour.ALWAYS_INVOICE, // this will create the immendiate invoice for the changes made
        },
      );
    }
  }

  /**
   * Creates a new tenant using the provided onboarding data.
   *
   * @param dto - The CreateTenantDTO containing tenant details.
   * @returns A promise resolving to the created tenant object.
   *
   * @throws {HttpErrors.BadRequest} If the contact information is not valid JSON.
   */
  async createTenant(fullDto: CreateTenantDTO) {
    const {leadId, ...dtoRest} = fullDto;
    const payload = dtoRest as CreateTenantDTO;
    this._verifyPayload(payload);

    const {isTrialApplied, ...dto} = payload;
    const selectedPlan = await this.subscriptionProxyService.findPlanById(
      this.token,
      dto.planId,
      {
        include: [
          {
            relation: 'currency',
          },
          {
            relation: 'billingCycle',
          },
        ],
      },
    );
    if (!selectedPlan) {
      throw new Error('selected plan does not exist');
    }
    const domains = [process.env.DOMAIN as string];

    const {files, numberOfUsers, totalCost, ...rest} = dto;
    console.info('Number of Users:', numberOfUsers);
    let fileRes: FileObject[] | undefined;

    if (dto.files) {
      fileRes = await this.fileAdapterService.generateFileResponse(
        files as unknown as FileMetadata | FileMetadata[],
      );
    }

    const newDto: TenantOnboardDTO = new TenantOnboardDTO({
      ...rest,
      domains,
      ...(fileRes ? {files: fileRes} : {}),
      planName: selectedPlan.name,
      planId: selectedPlan.id,
    });

    const tenant = await this.tenantMgmtProxyService.createTenant(
      this.token,
      newDto,
      leadId,
    );

    const customer = await this.subscriptionProxyService.createCustomer(
      this.token,
      {
        firstName: dto.contact.firstName,
        lastName: dto.contact.lastName,
        email: dto.contact.email,
        phone: dto.contact.phoneNumber,
        name: dto.name,
      } as CustomerDto,
      tenant.id,
    );

    if (isTrialApplied) {
      const trialDays = getTrialDays(selectedPlan);
      const subscription = await this._createSubscription(
        selectedPlan.id,
        tenant.id,
        0,
        0,
        'NA',
        SubscriptionStatus.TRIAL,
        new Date(Date.now() + trialDays * 24 * 60 * 60 * 1000).toISOString(),
      );
      await this.subscriptionProxyService.createInvoice(this.token, {
        customerId: customer.id,
        currencyCode: selectedPlan.currency?.currencyCode,
        charges: [
          new ChargeDto({
            amount: 0,
            description: `Trial for ${trialDays} days`,
          }),
        ],
      });

      const sdto: ISubscription = {
        id: subscription.id,
        subscriberId: subscription.subscriberId,
        startDate: subscription.startDate ?? 'NA',
        endDate: subscription.endDate ?? ' NA',
        planId: subscription.planId,
        status: subscription.status,
        plan: selectedPlan as unknown as IPlan,
      };

      const tempToken = this.cryptoHelperService.generateTempToken(
        {
          id: tenant.id,
          userTenantId: tenant.id,
          defaultTenantId: tenant.id,
          permissions: [PermissionKey.ProvisionTenant],
        },
        TEMP_TOKEN_EXPIRATION,
      );
      await this.tenantMgmtProxyService.provisionTenant(
        `Bearer ${tempToken}`,
        subscription.subscriberId,
        sdto,
      );

      console.log('provisioning successfull'); // NOSONAR

      return tenant;
    }
    const price = await this.subscriptionProxyService.createPrice(this.token, {
      product: selectedPlan.productRefId,
      recurring: {
        interval: selectedPlan.billingCycle?.durationUnit,
        //eslint-disable-next-line @typescript-eslint/naming-convention
        interval_count: selectedPlan.billingCycle?.duration,
      },
      currency: selectedPlan.currency?.currencyCode.toLowerCase(),
      active: true,
      //eslint-disable-next-line @typescript-eslint/naming-convention
      unit_amount: Math.round(dto.totalCost * 100), // Convert to cents
    });

    await this._createSubscription(
      dto.planId,
      tenant.id,
      dto.numberOfUsers,
      totalCost,
      price.id,
    );

    return tenant;
  }

  private async _createSubscription(
    planId: string,
    userId: string,
    numberOfUsers?: number,
    totalCost?: number,
    priceRefId?: string,
    status?: SubscriptionStatus,
    trialEndDate?: string,
  ) {
    const token = this.cryptoHelperService.generateTempToken(
      {
        id: userId,
        userTenantId: userId,
        permissions: [
          PermissionKey.ViewSubscription,
          PermissionKey.ViewPlan,
          PermissionKey.CreateSubscription,
          PermissionKey.CreateInvoice,
          '7029', // view plan sizes
          '7033', // view plan features
        ],
      },
      TEMP_TOKEN_EXPIRATION,
    );

    const createdSubscription =
      await this.subscriptionProxyService.createSubscriptions(
        `Bearer ${token}`,
        {
          planId,
          subscriberId: userId,
          status: status ?? SubscriptionStatus.PENDING,
          numberOfUsers: numberOfUsers
            ? parseInt(numberOfUsers as unknown as string, 10)
            : undefined,
          totalCost: totalCost ? parseFloat(totalCost as unknown as string) : 0,
          priceRefId,
          trialEndDate,
        },
      );
    return createdSubscription;
  }

  /**
   * Counts the number of tenants that match the specified criteria.
   *
   * @param where - Optional filter criteria to determine which tenants to count.
   * @returns A promise that resolves to the count of tenants matching the filter.
   */
  countTenants(where?: Where<Tenant>): Promise<Count> {
    return this.tenantMgmtProxyService.getTenantCount(this.token, where);
  }

  /**
   * Retrieves a list of tenants based on the provided filter criteria.
   *
   * @param filter - Optional filter object to specify query parameters for tenant retrieval.
   * @returns A promise that resolves to the list of tenants matching the filter.
   */
  async getTenants(filter?: Filter<Tenant>) {
    return this.tenantMgmtProxyService.getTenant(this.token, filter);
  }

  /**
   * Retrieves tenant details by tenant ID.
   *
   * @param tenantId - The ID of the tenant to retrieve details for.
   * @returns A promise that resolves to an object containing tenant details, contact info, files, address, and plan.
   */
  async getTenantDetails(tenantId: string) {
    const tenant = await this.tenantMgmtProxyService.getTenantById(
      this.token,
      tenantId,
    );

    // Extract tenant admin contact (assuming first contact is admin)
    const tenantAdmin = getContact(tenant);

    // Extract files and generate pre-signed URLs
    const rawFiles = Array.isArray(tenant.files) ? tenant.files : [];
    this.logger.info(`Found ${rawFiles.length} files for tenant ${tenantId}`);

    if (rawFiles.length > 0) {
      this.logger.info(
        `File keys found: ${rawFiles.map(f => f.fileKey).join(', ')}`,
      );
    }

    const filesWithSignedUrls = await Promise.all(
      rawFiles.map(async file => {
        try {
          this.logger.info(
            `Generating LocalStack view URL for file: ${file.originalName} (fileKey: ${file.fileKey})`,
          );
          const signedUrl =
            await this.fileAdapterService.generateLocalStackViewUrl(
              file.fileKey,
              file.originalName,
            );
          this.logger.info(
            `Generated LocalStack view URL: ${signedUrl?.substring(0, 100)}...`,
          );
          return {
            ...file,
            signedUrl: signedUrl,
          };
        } catch (error) {
          this.logger.error(
            `Error generating LocalStack view URL for file ${file.originalName}:`,
            error,
          );
          return {
            ...file,
            signedUrl: null,
          };
        }
      }),
    );

    // Extract address details - LoopBack populates belongsTo relations with the property name
    // Use type assertion to access the dynamic property
    const tenantWithRelations = tenant as Tenant & {address?: object};
    const address = tenantWithRelations.address ?? {};

    // Create a clean tenant details object without nested relations
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const {contacts, files: tenantFiles, ...tenantDetails} = tenant;

    return {
      tenantDetails: tenantDetails,
      contact: tenantAdmin,
      files: filesWithSignedUrls,
      address: address,
      plan: {}, // Empty object as requested
    };
  }

  /**
   * Retrieves all possible tenant statuses.
   *
   * @returns A promise that resolves to an array of TenantStatusDto objects.
   */
  async getAllStatuses(): Promise<StatusDto[]> {
    return this.tenantMgmtProxyService.getAllTenantStatus(this.token);
  }

  async getDashboardMetrics(
    filter?: Filter<Tenant>,
  ): Promise<DashboardMetricsDto> {
    const tempToken =
      'Bearer ' +
      this.cryptoHelperService.generateTempToken({
        userTenantId: process.env.CLIENT_ID,
        tenantId: process.env.DEFAULT_TENANT_ID,
        defaultTenantId: process.env.DEFAULT_TENANT_ID,
        permissions: [PermissionKey.ViewAllStatuses, PermissionKey.ViewTenant],
      });
    const status =
      await this.tenantMgmtProxyService.findAllStatusMetrics(tempToken);
    const tenants = await this.tenantMgmtProxyService.getTenant(
      tempToken,
      filter,
    );
    return {
      status,
      tenants,
    };
  }
}
