import {
  EventTypes,
  Notification,
  NotificationEventType,
  NotificationType,
  PermissionKey,
  PlanStatus,
  PlanTierType,
  ProrationBehaviour,
  StatusDto,
  SubscriptionStatus,
  TemplateService,
  TenantStatus,
  CryptoHelperService as LocalCryptoService,
  getCCEmailsForEvent,
} from '@local/core';
import {BindingScope, inject, injectable, service} from '@loopback/core';
import {AnyObject, Count, Filter, Where} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {ILogger, LOGGER} from '@sourceloop/core';
import {
  CreateTenantDTO,
  CustomerDto,
  EditTenantDTO,
  FileObject,
  Plan,
  KeySuggestionDto,
  Subscription,
  Tenant,
  TenantOnboardDTO,
  TriggerDto,
  UpdateTenantDto,
  VerifyKeyDto,
  SubscriptionWithRelations,
} from '../models';
import {FileMetadata} from '../types';
import {
  FileAdapterService,
  tenantKey,
  tenantWithoutIdKey,
} from './file-adapter.service';
import {ApplicationWebhookService} from './application-webhook.service';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from './proxies';
import {
  Contact,
  CryptoHelperService,
  IPlan,
  ISubscription,
} from '@sourceloop/ctrl-plane-tenant-management-service';
import {ChargeDto} from '@sourceloop/ctrl-plane-subscription-service';
const DEFAULT_TRIAL_DAYS = 14;
import {Route53ClientProxyService} from './proxies/route-53-client-proxy.service';
import {csvEntryFrom, PlanStatusNameMap} from '../util/csv.util';

const TEMP_TOKEN_EXPIRATION = 30000; // 30 seconds
/**
 * Returns the number of trial days from the selected plan metadata.
 * Defaults to DEFAULT_TRIAL_DAYS if not specified.
 *
 * @param {Plan} selectedPlan The selected plan object
 * @returns {number} Number of trial days
 */
function getTrialDays(selectedPlan: Plan) {
  return (
    (selectedPlan.metaData &&
      (selectedPlan.metaData as {trialDays?: number}).trialDays) ??
    DEFAULT_TRIAL_DAYS
  );
}

/**
 * Returns the primary contact of a tenant if available.
 * Returns an empty object if no contacts are present.
 *
 * @param {Tenant} tenant The tenant object
 * @returns {object} Contact object or empty object
 */
function getContact(tenant: Tenant): Contact | {} {
  return tenant.contacts && tenant.contacts.length > 0
    ? tenant.contacts[0]
    : {};
}

@injectable({scope: BindingScope.TRANSIENT})
/**
 * A helper service to manage tenant onboarding operations such as key verification,
 * tenant creation, and file handling.
 */
export class TenantHelperService {
  token: string;

  /**
   * Constructs a new instance of the TenantHelperService.
   *
   * @param request - The current HTTP request, used to extract the authorization token.
   * @param fileAdapterService - Service to manage file operations.
   * @param subscriptionProxyService - Proxy service to interact with the subscription service.
   * @param tenantMgmtProxyService - Proxy service to interact with the tenant management service.
   * @param logger - Logger for logging errors or messages.
   *
   * @throws {HttpErrors.Unauthorized} If the Authorization header is missing.
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.FileAdapterService')
    private readonly fileAdapterService: FileAdapterService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,
    @inject('services.CryptoHelperService')
    private readonly cryptoHelperService: CryptoHelperService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
    @inject('services.Route53ClientProxyService')
    private readonly route53ClientProxyService: Route53ClientProxyService,
    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,
    @service(TemplateService)
    private readonly templateService: TemplateService,
    @service(LocalCryptoService)
    private readonly localCryptoService: LocalCryptoService,
    @service(ApplicationWebhookService)
    private readonly applicationWebhookService: ApplicationWebhookService,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    } else {
      throw new HttpErrors.Unauthorized();
    }
  }

  async checkSubdomain(
    hostedZoneId: string,
    subdomain: string,
  ): Promise<boolean> {
    return this.route53ClientProxyService.checkSubdomain(
      hostedZoneId,
      subdomain,
    );
  }

  async reTriggerPipeline(id: string, triggerDto: TriggerDto): Promise<void> {
    const filter: Filter<Subscription> = {
      where: {
        subscriberId: id,
      },
      include: [
        {
          relation: 'plan',
          scope: {
            include: [
              {relation: 'currency'},
              {relation: 'billingCycle'},
              {relation: 'configureDevice'},
            ],
          },
        },
      ],
      order: ['createdOn DESC'],
    };
    const subs = await this.subscriptionProxyService.getSubscriptions(
      this.token,
      JSON.stringify(filter),
    );
    if (subs.length === 0) {
      throw new HttpErrors.NotFound(
        `No subscription found for tenant ID ${id}`,
      );
    }
    triggerDto.payload = {...triggerDto.payload, subscription: subs[0]};

    // Call the appropriate service to trigger the pipeline
    await this.tenantMgmtProxyService.triggerPipeline(
      this.token,
      id,
      triggerDto,
    );

    if (triggerDto.detailType === EventTypes.TENANT_MIGRATION) {
      await this.subscriptionProxyService.resumeSubscriptionById(
        this.token,
        subs[0].id,
      );
    }
  }

  /**
   * Triggers a pipeline for the specified tenant.
   *
   * @param id - The ID of the tenant for which to trigger the pipeline.
   * @param triggerDto - The DTO containing trigger details.
   * @returns A promise resolving to the result of the pipeline trigger.
   */
  async triggerPipeline(id: string, triggerDto: TriggerDto): Promise<void> {
    const tenantDetails = await this.tenantMgmtProxyService.getTenantById(
      this.token,
      id,
    );
    if (!tenantDetails) {
      throw new HttpErrors.NotFound(`Tenant with ID ${id} not found`);
    }

    // Call the appropriate service to trigger the pipeline
    await this.tenantMgmtProxyService.triggerPipeline(
      this.token,
      id,
      triggerDto,
    );
  }
  async sendInvite(id: string) {
    const token = this.cryptoHelperService.generateTempToken(
      {
        id: id,
        userTenantId: id,
        permissions: [
          PermissionKey.UpdateSubscription,
          PermissionKey.UpdateTenant,
        ],
      },
      TEMP_TOKEN_EXPIRATION,
    );
    await this.subscriptionProxyService.renewSubscriptionInvite(
      `Bearer ${token}`,
      id,
      SubscriptionStatus.PENDING_EXPIRED,
    );
    await this.tenantMgmtProxyService.updateTenant(
      `Bearer ${token}`,
      {
        status: TenantStatus.PENDINGPROVISION,
      },
      {
        id: id,
      },
    );
  }

  async triggerDeProvision(id: string) {
    const filter: Filter<Subscription> = {
      where: {
        subscriberId: id,
        status: {neq: SubscriptionStatus.CANCELLED},
      },
      include: [
        {
          relation: 'plan',
          scope: {
            include: [{relation: 'configureDevice'}],
          },
        },
      ],
    };
    const subscriptions = await this.subscriptionProxyService.getSubscriptions(
      this.token,
      JSON.stringify(filter),
    );

    if (subscriptions.length === 0) {
      throw new HttpErrors.NotFound(
        `No suspended subscription found for tenant ID ${id}`,
      );
    }
    await this.subscriptionProxyService.cancelSubscriptionById(
      this.token,
      subscriptions[0].id,
    );
    const noInfra = [
      SubscriptionStatus.PENDING,
      SubscriptionStatus.PENDING_EXPIRED,
    ];
    if (noInfra.includes(subscriptions[0].status)) {
      await this.tenantMgmtProxyService.updateTenant(
        this.token,
        {
          status: TenantStatus.INACTIVE,
        },
        {
          id: id,
        },
      );
    } else {
      await this.tenantMgmtProxyService.triggerPipeline(
        this.token,
        id,
        new TriggerDto({
          detailType: EventTypes.TENANT_DEPROVISIONING,
          payload: {subscription: subscriptions[0]},
        }),
      );

      console.info('De-Provisioning successful');
    }
  }

  async reActivateTenant(id: string) {
    const token = this.cryptoHelperService.generateTempToken(
      {
        id: id,
        userTenantId: id,
        permissions: [
          PermissionKey.UpdateSubscription,
          PermissionKey.UpdateTenant,
        ],
      },
      TEMP_TOKEN_EXPIRATION,
    );

    await this.subscriptionProxyService.renewSubscriptionInvite(
      `Bearer ${token}`,
      id,
      SubscriptionStatus.CANCELLED,
    );
    await this.tenantMgmtProxyService.updateTenant(
      `Bearer ${token}`,
      {
        status: TenantStatus.PENDINGPROVISION,
      },
      {
        id: id,
      },
    );
  }

  async triggerProvision(id: string) {
    const subscription = await this.findSuspendedSubscription(id);
    const sdto = this.buildSubscriptionDto(subscription);

    await this.tenantMgmtProxyService.provisionTenant(
      this.token,
      subscription.subscriberId,
      sdto,
    );

    await this.subscriptionProxyService.resumeSubscriptionById(
      this.token,
      subscription.id,
    );

    console.info('Re-Provisioning successful');
  }

  private async findSuspendedSubscription(
    id: string,
  ): Promise<SubscriptionWithRelations> {
    const filter: Filter<Subscription> = {
      where: {
        subscriberId: id,
        status: SubscriptionStatus.SUSPEND,
      },
      include: [
        {
          relation: 'plan',
          scope: {
            include: [
              {relation: 'currency'},
              {relation: 'billingCycle'},
              {relation: 'configureDevice'},
            ],
          },
        },
      ],
    };

    const subscriptions = await this.subscriptionProxyService.getSubscriptions(
      this.token,
      JSON.stringify(filter),
    );

    if (subscriptions.length === 0) {
      throw new HttpErrors.NotFound(
        `No suspended subscription found for tenant ID ${id}`,
      );
    }

    return subscriptions[0];
  }

  private buildSubscriptionDto(
    subscription: SubscriptionWithRelations,
  ): ISubscription {
    return {
      id: subscription.id,
      subscriberId: subscription.subscriberId,
      startDate: subscription.startDate ?? '',
      endDate: subscription.endDate ?? '',
      planId: subscription.planId,
      status: subscription.status,
      plan: {
        ...subscription.plan,
        id: subscription.planId ?? 'NA',
        name: subscription.plan?.name ?? 'NA',
        price: subscription.plan?.price ?? 0,
        currencyId: subscription.plan?.currencyId ?? 'NA',
        billingCycleId: subscription.plan?.billingCycleId ?? 'NA',
        metaData: {
          pipelineName: subscription.plan?.tier.toUpperCase() ?? 'NA',
        },
        tier: subscription.plan?.tier.toUpperCase() ?? 'NA',
      } as unknown as IPlan,
    };
  }

  /**
   * Verifies if a given key is valid for tenant onboarding.
   *
   * @param body - The DTO containing key details to verify.
   * @returns A promise resolving to the result of the key verification.
   */
  async verifyKey(body: VerifyKeyDto) {
    if (!process.env.HOSTED_ZONE_ID) {
      throw new HttpErrors.InternalServerError(
        'HOSTED_ZONE_ID is not defined in environment variables',
      );
    }

    if (!process.env.DOMAIN) {
      throw new HttpErrors.InternalServerError(
        'DOMAIN is not defined in environment variables',
      );
    }

    const [isSubdomainAvailableOnAWS, keySuggestionDto] = await Promise.all([
      this.checkSubdomain(
        process.env.HOSTED_ZONE_ID,
        `${body.key}.${process.env.DOMAIN}`,
      ),
      this.tenantMgmtProxyService.verifyKey(this.token, body),
    ]);

    if (isSubdomainAvailableOnAWS) {
      if (!keySuggestionDto.available) {
        return keySuggestionDto;
      } else {
        return {
          available: false,
        } as KeySuggestionDto;
      }
    } else {
      return keySuggestionDto;
    }
  }

  /**
   * Verifies and normalizes the tenant payload for create or edit operations.
   *
   * - Parses the `contact` property if it's a JSON string.
   * - Ensures invalid JSON throws a `BadRequest` error.
   * - Cleans up empty `phoneNumber` and `designation` fields by setting them to `undefined`.
   *
   * @private
   * @param {CreateTenantDTO | EditTenantDTO} dto - The tenant DTO to validate and normalize.
   * @throws {HttpErrors.BadRequest} If the `contact` field is not a valid JSON string.
   */
  private _verifyPayload(dto: CreateTenantDTO | EditTenantDTO) {
    if (typeof dto.contact === 'string') {
      try {
        dto.contact = JSON.parse(dto.contact);
      } catch (error) {
        this.logger.error(JSON.stringify(error));
        throw new HttpErrors.BadRequest('Invalid JSON format for contact');
      }
    }

    if (dto.contact) {
      if (dto.contact.phoneNumber === '') {
        dto.contact.phoneNumber = undefined;
      }

      if (dto.contact.designation === '') {
        dto.contact.designation = undefined;
      }
    }
  }

  /**
   * Parses and validates the `existingFiles` field in the tenant DTO.
   *
   * - If `existingFiles` is a JSON string, it is parsed into a `FileObject[]`.
   * - Logs and throws a `BadRequest` error for invalid JSON.
   *
   * @private
   * @param {EditTenantDTO} dto - The tenant DTO containing `existingFiles`.
   * @returns {FileObject[]} The parsed list of file objects.
   * @throws {HttpErrors.BadRequest} If the `existingFiles` field is not valid JSON.
   */
  private parseExistingFiles(dto: EditTenantDTO): FileObject[] {
    if (typeof dto.existingFiles === 'string') {
      try {
        return JSON.parse(dto.existingFiles);
      } catch (error) {
        this.logger.error(JSON.stringify(error));
        throw new HttpErrors.BadRequest(
          'Invalid JSON format for existing files',
        );
      }
    }
    return dto.existingFiles;
  }
  /**
   * Checks whether a subscription downgrade is allowed between tiers.
   *
   * Specifically, it prevents downgrading from the `PREMIUM` plan tier
   * to the `STANDARD` plan tier. If such a downgrade is attempted,
   * an `HttpErrors.UnprocessableEntity` is thrown.
   *
   * @param {PlanTierType} prevTier - The previous subscription tier.
   * @param {PlanTierType} newTier - The new subscription tier to be applied.
   * @throws {HttpErrors.UnprocessableEntity} When downgrading from PREMIUM to STANDARD.
   * @returns {Promise<void>} Resolves if the downgrade is valid, otherwise throws an error.
   */
  private async checkForDowngrade(
    prevTier: PlanTierType,
    newTier: PlanTierType,
  ) {
    if (
      prevTier === PlanTierType.PREMIUM &&
      newTier === PlanTierType.STANDARD
    ) {
      throw new HttpErrors.UnprocessableEntity(
        'You cannot downgrade from premium to standard tier',
      );
    }
  }

  /**
   * Updates an existing tenant with new details.
   *
   * - Verifies and normalizes the DTO payload.
   * - Parses existing files and generates new file responses if provided.
   * - Fetches the selected subscription plan and validates its existence.
   * - Updates the tenant record via the tenant management proxy service.
   * - Validates the existing active subscription for the tenant.
   * - If plan or cost has changed, updates the subscription with new pricing details.
   *
   * @async
   * @param {EditTenantDTO} dto - The tenant details to be updated.
   * @param {string} tenantId - The unique identifier of the tenant to update.
   * @returns {Promise<void>} A promise that resolves when the tenant update is complete.
   *
   * @throws {Error} If the selected plan does not exist.
   * @throws {Error} If no active subscription exists for the tenant.
   * @throws {HttpErrors.BadRequest} If file or contact fields contain invalid JSON.
   */
  async editTenant(dto: EditTenantDTO, tenantId: string): Promise<Tenant> {
    this._verifyPayload(dto);
    dto.existingFiles = this.parseExistingFiles(dto);

    const selectedPlan = await this.subscriptionProxyService.findPlanById(
      this.token,
      dto.planId,
      {
        include: [
          {
            relation: 'currency',
          },
          {
            relation: 'billingCycle',
          },
          {
            relation: 'configureDevice',
          },
        ],
      },
    );
    if (!selectedPlan) {
      throw new Error('selected plan does not exist');
    }

    const {files, existingFiles, numberOfUsers, totalCost, planId, ...rest} =
      dto;

    const fileRes: FileObject[] | undefined =
      await this.fileAdapterService.generateFileResponse(
        files ? (files as unknown as FileMetadata | FileMetadata[]) : [],
      );

    const response = await this.applicationWebhookService.updateContact(
      tenantId,
      dto,
    );
    const allFiles = [
      ...fileRes,
      ...existingFiles.map(file => ({
        fileKey: file.fileKey,
        originalName: file.originalName,
        source: file.source,
        size: file.size,
      })),
    ];

    const subscription = await this.subscriptionProxyService.getSubscriptions(
      this.token,
      JSON.stringify({
        where: {
          subscriberId: tenantId,
          status: {
            inq: [
              SubscriptionStatus.ACTIVE,
              SubscriptionStatus.PENDING,
              SubscriptionStatus.TRIAL,
              SubscriptionStatus.TRIAL_REMINDER_SENT,
              SubscriptionStatus.TRIAL_SUSPEND,
              SubscriptionStatus.PAID_PLAN_SELECTED,
            ],
          },
        },
        include: [
          {
            relation: 'plan',
            scope: {
              include: [
                {
                  relation: 'currency',
                },
                {
                  relation: 'billingCycle',
                },
                {
                  relation: 'configureDevice',
                },
              ],
            },
          },
        ],
      }),
    );
    if (subscription.length <= 0) {
      throw new Error('subscription does not exist');
    }

    rest.contact = this.updateContactDto(response, rest);

    await this.checkForDowngrade(subscription[0].plan.tier, selectedPlan.tier);
    const newDto = new UpdateTenantDto({
      ...rest,
      selectedFiles: allFiles,
      planName: selectedPlan.name,
      planId: selectedPlan.id,
    });
    if (Number(subscription[0].plan.status) === PlanStatus.TRIAL) {
      newDto.status = TenantStatus.PENDINGPROVISION;
    }

    await this.tenantMgmtProxyService.updateTenantById(
      this.token,
      tenantId,
      newDto,
    );

    if (
      (dto.planId && dto.planId !== subscription[0].planId) ||
      (dto.totalCost && dto.totalCost !== subscription[0].totalCost)
    ) {
      const price = await this.subscriptionProxyService.createPrice(
        this.token,
        {
          product: selectedPlan.productRefId,
          recurring: {
            interval: selectedPlan.billingCycle?.durationUnit,
            //eslint-disable-next-line @typescript-eslint/naming-convention
            interval_count: selectedPlan.billingCycle?.duration,
          },
          currency: selectedPlan.currency?.currencyCode.toLowerCase(),
          active: true,
          //eslint-disable-next-line @typescript-eslint/naming-convention
          unit_amount: Math.round(dto.totalCost * 100), // Convert to cents
        },
      );

      await this.subscriptionProxyService.updateSubscriptionById(
        this.token,
        subscription[0].id,
        {
          priceRefId: price.id,
          totalCost: Number(totalCost),
          numberOfUsers: numberOfUsers ? Number(numberOfUsers) : undefined,
          planId,
          prorationBehavior: ProrationBehaviour.ALWAYS_INVOICE, // this will create the immendiate invoice for the changes made
        },
      );

      await this.sendMailOnEditTenantPlan(selectedPlan, dto, tenantId);
    }
    await this.appendOrUpdateCsvEntry(dto, tenantId, selectedPlan, true);
    return this.tenantMgmtProxyService.getTenantById(this.token, tenantId);
  }

  updateContactDto(pwd: string | null | undefined, dto: AnyObject) {
    if (pwd) {
      return {
        ...dto.contact,
        password: pwd,
      };
    }
    return dto.contact;
  }

  private formatDate = (today: Date = new Date()) => {
    const formattedDate = today.toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
    const parts = formattedDate.replace(',', '').split(' ');
    return `${parts[1]} ${parts[0]}, ${parts[2]}`;
  };

  async sendMailOnEditTenantPlan(
    selectedPlan: Plan,
    dto: EditTenantDTO,
    tenantId: string,
  ) {
    const planName = selectedPlan.name;
    const devicesCount =
      selectedPlan.configureDevice.min +
      '-' +
      selectedPlan.configureDevice?.max;

    const tenantData = await this.tenantMgmtProxyService.getTenantById(
      this.token,
      tenantId,
      {
        include: [
          {
            relation: 'contacts',
          },
        ],
      },
    );
    const noOfUsers = selectedPlan.allowedUnlimitedUsers
      ? 'unlimited'
      : (dto.numberOfUsers ?? 0);

    const tenure = selectedPlan.billingCycle.cycleName;

    const token = this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.SYSTEM_USER_TENANT_ID,
      tenantId: process.env.SYSTEM_USER_TENANT_ID,
      defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
      permissions: [
        PermissionKey.CreateNotification,
        PermissionKey.ViewNotificationTemplate,
      ],
    });

    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.TenantEditPlan,
      NotificationType.EMAIL,
      token,
    );

    if (!template)
      throw new HttpErrors.InternalServerError(
        'Notification template not found',
      );

    const emailSubject = `Your Tenant Account Details Have Been Updated`;
    const finalDate = this.formatDate();
    const emailBody = this.templateService.generateEmail(template.body, {
      DISTEK_LOGO: process.env.DISTEK_LOGO ?? '',
      TENANT_NAME: tenantData.name,
      USER_NAME: `${tenantData.contacts[0]?.firstName}`,
      PLAN_NAME: planName,
      NO_OF_DEVICES: devicesCount,
      TENURE: tenure,
      TOTAL_COST: dto.totalCost,
      NO_OF_USERS: noOfUsers,
      UPDATED_ON: finalDate,
      SUPPORT_EMAIL: process.env.SUPPORT_EMAIL ?? '',
      HREF_SUPPORT: `mailto:${process.env.SUPPORT_EMAIL}`,
    });
    const ccEmails = getCCEmailsForEvent(template.eventName);

    const notification = new Notification({
      subject: emailSubject,
      body: emailBody,
      receiver: {
        to: [
          {id: tenantData.id, toEmail: tenantData.contacts?.[0]?.email ?? ''},
        ],
        cc: ccEmails,
      },
      type: 1,
      sentDate: new Date(),
      options: {
        fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
      },
    });

    await this.notificationProxyService.createNotification(token, notification);

    return notification;
  }

  /**
   * Creates a new tenant using the provided onboarding data.
   *
   * @param dto - The CreateTenantDTO containing tenant details.
   * @returns A promise resolving to the created tenant object.
   *
   * @throws {HttpErrors.BadRequest} If the contact information is not valid JSON.
   */
  async createTenant(fullDto: CreateTenantDTO) {
    const {leadId, ...dtoRest} = fullDto;
    const payload = dtoRest as CreateTenantDTO;
    this._verifyPayload(payload);

    const {isTrialApplied, ...dto} = payload;
    const selectedPlan = await this.subscriptionProxyService.findPlanById(
      this.token,
      dto.planId,
      {
        include: [
          {
            relation: 'currency',
          },
          {
            relation: 'billingCycle',
          },
          {
            relation: 'configureDevice',
          },
        ],
      },
    );
    if (!selectedPlan) {
      throw new Error('selected plan does not exist');
    }
    const domains = [process.env.DOMAIN as string];

    const {files, numberOfUsers, totalCost, ...rest} = dto;
    console.info('Number of Users:', numberOfUsers);
    let fileRes: FileObject[] | undefined;

    if (dto.files) {
      fileRes = await this.fileAdapterService.generateFileResponse(
        files as unknown as FileMetadata | FileMetadata[],
      );
    }

    const newDto: TenantOnboardDTO = new TenantOnboardDTO({
      ...rest,
      domains,
      ...(fileRes ? {files: fileRes} : {}),
      planName: selectedPlan.name,
      planId: selectedPlan.id,
    });

    const tenant = await this.tenantMgmtProxyService.createTenant(
      this.token,
      newDto,
      leadId,
    );

    const customer = await this.subscriptionProxyService.createCustomer(
      this.token,
      {
        firstName: dto.contact.firstName,
        lastName: dto.contact.lastName,
        email: dto.contact.email,
        phone: dto.contact.phoneNumber,
        name: dto.name,
      } as CustomerDto,
      tenant.id,
    );

    if (isTrialApplied) {
      const trialDays = getTrialDays(selectedPlan);
      const subscription = await this._createSubscription(
        selectedPlan.id,
        tenant.id,
        0,
        0,
        'NA',
        SubscriptionStatus.TRIAL,
        new Date(Date.now() + trialDays * 24 * 60 * 60 * 1000).toISOString(),
      );
      await this.subscriptionProxyService.createInvoice(this.token, {
        customerId: customer.id,
        currencyCode: selectedPlan.currency?.currencyCode,
        charges: [
          new ChargeDto({
            amount: 0,
            description: `Trial for ${trialDays} days`,
          }),
        ],
      });

      const sdto: ISubscription = {
        id: subscription.id,
        subscriberId: subscription.subscriberId,
        startDate: subscription.startDate ?? 'NA',
        endDate: subscription.endDate ?? ' NA',
        planId: subscription.planId,
        status: subscription.status,
        plan: selectedPlan as unknown as IPlan,
      };

      const tempToken = this.cryptoHelperService.generateTempToken(
        {
          id: tenant.id,
          userTenantId: tenant.id,
          defaultTenantId: tenant.id,
          permissions: [PermissionKey.ProvisionTenant],
        },
        TEMP_TOKEN_EXPIRATION,
      );
      await this.tenantMgmtProxyService.provisionTenant(
        `Bearer ${tempToken}`,
        subscription.subscriberId,
        sdto,
      );

      console.log('provisioning successfull'); // NOSONAR

      return tenant;
    }
    const price = await this.subscriptionProxyService.createPrice(this.token, {
      product: selectedPlan.productRefId,
      recurring: {
        interval: selectedPlan.billingCycle?.durationUnit,
        //eslint-disable-next-line @typescript-eslint/naming-convention
        interval_count: selectedPlan.billingCycle?.duration,
      },
      currency: selectedPlan.currency?.currencyCode.toLowerCase(),
      active: true,
      //eslint-disable-next-line @typescript-eslint/naming-convention
      unit_amount: Math.round(dto.totalCost * 100), // Convert to cents
    });

    await this._createSubscription(
      dto.planId,
      tenant.id,
      dto.numberOfUsers,
      totalCost,
      price.id,
    );

    await this.appendOrUpdateCsvEntry(payload, tenant.id, selectedPlan);

    return tenant;
  }

  private async _createSubscription(
    planId: string,
    userId: string,
    numberOfUsers?: number,
    totalCost?: number,
    priceRefId?: string,
    status?: SubscriptionStatus,
    trialEndDate?: string,
  ) {
    const token = this.cryptoHelperService.generateTempToken(
      {
        id: userId,
        userTenantId: userId,
        permissions: [
          PermissionKey.ViewSubscription,
          PermissionKey.ViewPlan,
          PermissionKey.CreateSubscription,
          PermissionKey.CreateInvoice,
          '7029', // view plan sizes
          '7033', // view plan features
        ],
      },
      TEMP_TOKEN_EXPIRATION,
    );

    const createdSubscription =
      await this.subscriptionProxyService.createSubscriptions(
        `Bearer ${token}`,
        {
          planId,
          subscriberId: userId,
          status: status ?? SubscriptionStatus.PENDING,
          numberOfUsers: numberOfUsers
            ? Number.parseInt(numberOfUsers as unknown as string, 10)
            : undefined,
          totalCost: totalCost
            ? Number.parseFloat(totalCost as unknown as string)
            : 0,
          priceRefId,
          trialEndDate,
        },
      );
    return createdSubscription;
  }

  /**
   * Counts the number of tenants that match the specified criteria.
   *
   * @param where - Optional filter criteria to determine which tenants to count.
   * @returns A promise that resolves to the count of tenants matching the filter.
   */
  countTenants(where?: Where<Tenant>): Promise<Count> {
    return this.tenantMgmtProxyService.getTenantCount(this.token, where);
  }

  private async createCSVIfNotExists() {
    if (await this.fileAdapterService.checkIfKeyExists(tenantKey)) {
      return;
    } else {
      const result = await this.getTenants({
        include: [{relation: 'contacts'}, {relation: 'address'}],
        order: ['createdOn'],
      });
      const tenants = result.map(async tenant => {
        // Fetch contact from tenant.contacts[0] if available

        const filter: Filter<Subscription> = {
          where: {
            subscriberId: tenant.id,
          },
          include: [
            {
              relation: 'plan',
            },
          ],
        };
        const subscriptions =
          await this.subscriptionProxyService.getSubscriptions(
            this.token,
            JSON.stringify(filter),
          );
        const subscription =
          subscriptions?.length > 0 ? subscriptions[0] : undefined;
        return csvEntryFrom(tenant, subscription);
      });
      await this.fileAdapterService.appendOrUpdateTenantRowToS3(
        await Promise.all(tenants),
      );
    }
  }

  /**
   * Retrieves a list of tenants based on the provided filter criteria.
   *
   * @param filter - Optional filter object to specify query parameters for tenant retrieval.
   * @returns A promise that resolves to the list of tenants matching the filter.
   */
  async getTenants(filter?: Filter<Tenant>) {
    return this.tenantMgmtProxyService.getTenant(this.token, filter);
  }

  /**
   * Retrieves tenant details by tenant ID.
   *
   * @param tenantId - The ID of the tenant to retrieve details for.
   * @returns A promise that resolves to an object containing tenant details, contact info, files, address, and plan.
   */
  async getTenantDetails(tenantId: string) {
    const tenant = await this.tenantMgmtProxyService.getTenantById(
      this.token,
      tenantId,
    );

    // Extract tenant admin contact (assuming first contact is admin)
    const tenantAdmin = getContact(tenant);

    // Extract files and generate pre-signed URLs
    const rawFiles = Array.isArray(tenant.files) ? tenant.files : [];
    this.logger.info(`Found ${rawFiles.length} files for tenant ${tenantId}`);

    if (rawFiles.length > 0) {
      this.logger.info(
        `File keys found: ${rawFiles.map(f => f.fileKey).join(', ')}`,
      );
    }

    const filesWithSignedUrls = await Promise.all(
      rawFiles.map(async file => {
        try {
          this.logger.info(
            `Generating LocalStack view URL for file: ${file.originalName} (fileKey: ${file.fileKey})`,
          );
          const signedUrl =
            await this.fileAdapterService.generateLocalStackViewUrl(
              file.fileKey,
              file.originalName,
            );
          this.logger.info(
            `Generated LocalStack view URL: ${signedUrl?.substring(0, 100)}...`,
          );
          return {
            ...file,
            signedUrl: signedUrl,
          };
        } catch (error) {
          this.logger.error(
            `Error generating LocalStack view URL for file ${file.originalName}:`,
            error,
          );
          return {
            ...file,
            signedUrl: null,
          };
        }
      }),
    );

    // Extract address details - LoopBack populates belongsTo relations with the property name
    // Use type assertion to access the dynamic property
    const tenantWithRelations = tenant as Tenant & {address?: object};
    const address = tenantWithRelations.address ?? {};

    // Create a clean tenant details object without nested relations
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const {contacts, files: tenantFiles, ...tenantDetails} = tenant;

    return {
      tenantDetails: tenantDetails,
      contact: tenantAdmin,
      files: filesWithSignedUrls,
      address: address,
      plan: {}, // Empty object as requested
    };
  }

  /**
   * Retrieves all possible tenant statuses.
   *
   * @returns A promise that resolves to an array of TenantStatusDto objects.
   */
  async getAllStatuses(): Promise<StatusDto[]> {
    return this.tenantMgmtProxyService.getAllTenantStatus(this.token);
  }

  /**
   * Retrieves the tenant CSV file information with a signed URL.
   * If the file does not exist, attempts to create it and retries the retrieval.
   * Throws a `NotFound` error if the file is still not found after creation.
   * Throws an `InternalServerError` for other errors encountered during the process.
   *
   * @param throwReoccurError - If `true`, throws a `NotFound` error when the CSV file is missing.
   * @returns A promise that resolves to an object containing file information and a signed URL.
   * @throws {HttpErrors.NotFound} If the tenant CSV file is not found after creation.
   * @throws {HttpErrors.InternalServerError} For other errors during file retrieval.
   */
  async getTenantCsvFile(throwReoccurError = false): Promise<AnyObject> {
    try {
      return await this.fileAdapterService.generateFileInfoWithSignedUrl(
        tenantWithoutIdKey,
      );
    } catch (error) {
      if ('name' in error && error.name === 'NotFound') {
        if (throwReoccurError) {
          throw new HttpErrors.NotFound('Tenant CSV file not found');
        }
        await this.createCSVIfNotExists();
        // try get the file again after creating it
        return this.getTenantCsvFile(true);
      } else {
        throw new HttpErrors.InternalServerError(
          'Error fetching tenant CSV file',
        );
      }
    }
  }

  async returnToken(tenantId: string, tenantSecret: string) {
    return this.localCryptoService.generateHmacSHA256(tenantSecret, tenantId);
  }

  /**
   * Updates a CSV entry for a tenant with the provided details and appends it to S3 storage.
   *
   * @param dto - The data transfer object containing tenant information to update.
   * @param id - The unique identifier of the tenant.
   * @param plan - (Optional) The plan details associated with the tenant.
   * @returns A promise that resolves when the CSV row has been appended to S3.
   */
  /**
   * Appends a tenant entry to the CSV file in S3.
   * Handles both CreateTenantDTO and EditTenantDTO.
   *
   * @param dto - The tenant DTO (CreateTenantDTO or EditTenantDTO).
   * @param id - The tenant ID.
   * @param plan - (Optional) The plan details.
   * @returns Promise<void>
   */
  async appendOrUpdateCsvEntry(
    dto: CreateTenantDTO | EditTenantDTO,
    id: string,
    plan?: Plan,
    isEdit = false,
  ) {
    // Common fields
    const baseObj = {
      id,
      planName: plan?.name ?? '',
      planTier: plan?.tier ?? '',
      planPrice: (plan?.price ?? 0).toString(),
      planStatus: PlanStatusNameMap[plan?.status ?? PlanStatus.TRIAL],
      allowedUnlimitedUsers: plan?.allowedUnlimitedUsers ?? false,
      version: plan?.version ?? '',
      numberOfUsers: dto.numberOfUsers,
      totalCost: dto.totalCost,
      firstName: dto.contact.firstName,
      lastName: dto.contact.lastName,
      phoneNumber: dto.contact.phoneNumber,
      designation: dto.contact.designation,
      city: dto.city,
      state: dto.state,
      userName: dto.contact.userName ?? '',
      countryCode: dto.contact.countryCode ?? '',
      perPersonCost: plan?.costPerUser,
    };
    // Helper type guard
    function isCreateTenantDTO(
      obj: CreateTenantDTO | EditTenantDTO,
    ): obj is CreateTenantDTO {
      return typeof (obj as CreateTenantDTO).name === 'string' && !isEdit;
    }
    // Add CreateTenantDTO-specific fields if present
    if (isCreateTenantDTO(dto)) {
      // Use object spread to add CreateTenantDTO fields
      const createFields = {
        name: dto.name,
        address: dto.address,
        zip: dto.zip,
        country: dto.country,
        email: dto.contact?.email,
      };
      Object.assign(baseObj, createFields);
    }

    await this.createCSVIfNotExists();

    return this.fileAdapterService.appendOrUpdateTenantRowToS3(baseObj);
  }
}
