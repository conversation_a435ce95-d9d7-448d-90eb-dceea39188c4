import {BindingScope, inject, injectable, service} from '@loopback/core';
import {
  CryptoHelperService,
  EventTypes,
  getCCEmailsForEvent,
  Notification,
  NotificationEventType,
  NotificationType,
  PermissionKey,
  SubscriptionStatus,
  TemplateService,
} from '@local/core';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from './proxies';
import {Filter} from '@loopback/repository';
import {Subscription, Tenant, TriggerDto} from '../models';
import {HttpErrors} from '@loopback/rest';

const THREE = 3;
const TWO = 2;

@injectable({scope: BindingScope.TRANSIENT})
export class SubscriptionHelperService {
  constructor(
    @inject('services.CryptoHelperService')
    private readonly cryptoHelperService: CryptoHelperService,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,
    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,
    @service(TemplateService)
    private readonly templateService: TemplateService,
  ) {}

  /**
   * Updates trial plan subscriptions by sending reminders,
   * suspending expired trials, and sending notifications.
   */
  async updateTrialPlan() {
    const token = this.generateSystemToken();

    const tenantMap = await this.fetchTenantsOnTrialPlan(token);
    const trialSubscriptions = await this.fetchTrialSubscriptions(token);

    const dueDays = this.getConfigNumber('DUE_DAYS_PENDING_REMINDER', THREE);
    const bufferDays = this.getConfigNumber('BUFFER_ALLOWED_DAYS', TWO);

    const {updatePromises, tenantsToNotify} = this.processSubscriptions(
      trialSubscriptions,
      tenantMap,
      dueDays,
      bufferDays,
      token,
    );

    await Promise.all(updatePromises);

    if (tenantsToNotify.length) {
      await this.sendTrialEndingNotifications(tenantsToNotify, dueDays, token);
    }
  }

  /** -------------------- Helpers -------------------- */

  /**
   * Generates a system-level token with predefined permissions.
   * @returns {string} Generated temporary token
   */
  private generateSystemToken(): string {
    return this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.SYSTEM_USER_TENANT_ID,
      tenantId: process.env.SYSTEM_USER_TENANT_ID,
      defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
      permissions: [
        PermissionKey.UpdateSubscription,
        PermissionKey.UpdatePlan,
        PermissionKey.ViewSubscription,
        PermissionKey.ViewPlan,
        PermissionKey.ViewTenant,
        PermissionKey.CreateNotification,
        PermissionKey.ViewNotificationTemplate,
        PermissionKey.UpdateTenant,
      ],
    });
  }

  /**
   * Fetches tenants that are on a trial plan.
   * @param {string} token Authorization token
   * @returns {Promise<Map<string, Tenant>>} Map of tenantId to Tenant
   */
  private async fetchTenantsOnTrialPlan(
    token: string,
  ): Promise<Map<string, Tenant>> {
    const tenants = await this.tenantMgmtProxyService.getTenant(
      `Bearer ${token}`,
      {
        include: [{relation: 'contacts'}],
      },
    );
    return new Map(tenants.map((tenant: Tenant) => [tenant.id, tenant]));
  }

  /**
   * Fetches subscriptions in trial-related statuses.
   * @param {string} token Authorization token
   * @returns {Promise<Subscription[]>} List of trial subscriptions
   */
  private async fetchTrialSubscriptions(
    token: string,
  ): Promise<Subscription[]> {
    const filter: Filter<Subscription> = {
      where: {
        status: {
          inq: [
            SubscriptionStatus.TRIAL,
            SubscriptionStatus.TRIAL_SUSPEND,
            SubscriptionStatus.TRIAL_REMINDER_SENT,
          ],
        },
      },
      include: [
        {
          relation: 'plan',
          scope: {
            include: [{relation: 'configureDevice'}],
          },
        },
      ],
    };
    return this.subscriptionProxyService.getSubscriptions(
      `Bearer ${token}`,
      JSON.stringify(filter),
    );
  }

  /**
   * Reads an environment variable as a number, or returns a default.
   * @param {string} envKey Environment variable key
   * @param {number} defaultValue Default value if env not set
   * @returns {number}
   */
  private getConfigNumber(envKey: string, defaultValue: number): number {
    return parseInt(process.env[envKey] ?? `${defaultValue}`, 10);
  }

  /**
   * Processes subscriptions to determine updates and notifications.
   * @param {Subscription[]} subscriptions List of subscriptions
   * @param {Map<string, Tenant>} tenantMap Map of tenantId to Tenant
   * @param {number} dueDays Days until due reminder
   * @param {number} bufferDays Buffer period after trial end
   * @param {string} token Authorization token
   * @returns Object containing update promises and tenants to notify
   */
  private processSubscriptions(
    subscriptions: Subscription[],
    tenantMap: Map<string, Tenant>,
    dueDays: number,
    bufferDays: number,
    token: string,
  ) {
    const updatePromises: Promise<unknown>[] = [];
    const tenantsToNotify: Tenant[] = [];
    const now = new Date();

    for (const sub of subscriptions) {
      if (!sub.trialEndDate) continue;

      const trialEnd = new Date(sub.trialEndDate);
      const result = this.handleSubscriptionTransition(
        sub,
        trialEnd,
        now,
        dueDays,
        bufferDays,
        tenantMap,
        token,
      );
      if (result?.updatePromise !== undefined) {
        updatePromises.push(result.updatePromise);
      }
      if (result?.triggerPromise !== undefined) {
        updatePromises.push(result.triggerPromise);
      }

      if (result?.tenantToNotify !== undefined) {
        tenantsToNotify.push(result.tenantToNotify);
      }
    }

    return {updatePromises, tenantsToNotify};
  }

  /**
   * Determines subscription status transitions and associated actions.
   * @param {Subscription} sub The subscription object
   * @param {Date} trialEnd Trial end date
   * @param {Date} now Current date
   * @param {number} dueDays Reminder threshold in days
   * @param {number} bufferDays Trial expiration buffer
   * @param {Map<string, Tenant>} tenantMap Map of tenantId to Tenant
   * @param {string} token Authorization token
   * @returns Update info or null
   */
  private handleSubscriptionTransition(
    sub: Subscription,
    trialEnd: Date,
    now: Date,
    dueDays: number,
    bufferDays: number,
    tenantMap: Map<string, Tenant>,
    token: string,
  ): {
    updatePromise?: Promise<unknown>;
    tenantToNotify?: Tenant;
    triggerPromise?: Promise<unknown>;
  } | null {
    switch (sub.status) {
      case SubscriptionStatus.TRIAL:
        if (this.shouldSendReminder(trialEnd, now, dueDays)) {
          return {
            updatePromise: this.updateSubscriptionStatus(
              token,
              sub.id,
              SubscriptionStatus.TRIAL_REMINDER_SENT,
            ),
            tenantToNotify: tenantMap.get(sub.subscriberId),
          };
        }
        break;

      case SubscriptionStatus.TRIAL_REMINDER_SENT:
        if (now >= trialEnd) {
          return {
            updatePromise: this.updateSubscriptionStatus(
              token,
              sub.id,
              SubscriptionStatus.TRIAL_SUSPEND,
            ),
            triggerPromise: this.triggerPipeline(
              token,
              sub.subscriberId,
              new TriggerDto({
                detailType: EventTypes.TENANT_SUSPENSION,
                payload: {subscription: sub},
              }),
            ),
          };
        }
        break;

      case SubscriptionStatus.TRIAL_SUSPEND:
        if (this.isTrialExpired(trialEnd, now, bufferDays)) {
          return {
            updatePromise: this.updateSubscriptionStatus(
              token,
              sub.id,
              SubscriptionStatus.TRIAL_EXPIRED,
            ),

            triggerPromise: this.triggerPipeline(
              token,
              sub.subscriberId,
              new TriggerDto({
                detailType: EventTypes.TENANT_DEPROVISIONING,
                payload: {subscription: sub},
              }),
            ),
          };
        }
        break;
    }
    return null;
  }

  /**
   * Determines if reminder should be sent based on dueDays.
   * @param {Date} trialEndDate Trial end date
   * @param {Date} now Current date
   * @param {number} dueDays Days threshold for reminder
   * @returns {boolean}
   */
  private shouldSendReminder(
    trialEndDate: Date,
    now: Date,
    dueDays: number,
  ): boolean {
    const reminderDate = new Date(trialEndDate);
    reminderDate.setDate(reminderDate.getDate() - dueDays);
    return now >= reminderDate && now < trialEndDate;
  }

  /**
   * Determines if trial has expired beyond bufferDays.
   * @param {Date} trialEndDate Trial end date
   * @param {Date} now Current date
   * @param {number} bufferDays Buffer period after trial end
   * @returns {boolean}
   */
  private isTrialExpired(
    trialEndDate: Date,
    now: Date,
    bufferDays: number,
  ): boolean {
    const expiryDate = new Date(trialEndDate);
    expiryDate.setDate(expiryDate.getDate() + bufferDays);
    return now > expiryDate;
  }

  /**
   * Updates subscription status.
   * @param {string} token Authorization token
   * @param {string} subscriptionId Subscription ID
   * @param {SubscriptionStatus} status New status to apply
   * @returns {Promise<unknown>}
   */
  private updateSubscriptionStatus(
    token: string,
    subscriptionId: string,
    status: SubscriptionStatus,
  ) {
    return this.subscriptionProxyService.updateSubscription(
      `Bearer ${token}`,
      {status},
      {id: subscriptionId},
    );
  }

  private triggerPipeline(
    token: string,
    subscriberId: string,
    arg2: TriggerDto,
  ): Promise<unknown> | undefined {
    return this.tenantMgmtProxyService.triggerPipeline(
      `Bearer ${token}`,
      subscriberId,
      arg2,
    );
  }

  /**
   * Sends notifications to tenants whose trials are ending.
   * @param {Tenant[]} tenants Tenants to notify
   * @param {number} dueDays Days left in trial
   * @param {string} token Authorization token
   */
  private async sendTrialEndingNotifications(
    tenants: Tenant[],
    dueDays: number,
    token: string,
  ) {
    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.TrialEndingSoon,
      NotificationType.EMAIL,
      token,
    );

    if (!template) {
      throw new HttpErrors.InternalServerError(
        'Notification template for trial ending not found',
      );
    }

    const today = new Date();
    const dueDate = new Date(today.getTime() + dueDays * 24 * 60 * 60 * 1000);
    const formattedDate = dueDate.toLocaleDateString('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });

    const parts = formattedDate.replace(',', '').split(' ');
    const finalDate = `${parts[1]} ${parts[0]}, ${parts[2]}`;
    const notifications = tenants.map(tenant => {
      const body = this.templateService.generateEmail(template.body, {
        DISTEK_LOGO: process.env.DISTEK_LOGO ?? '',
        TENANT_NAME: tenant.name,
        USER_NAME: tenant.contacts?.[0]?.firstName ?? '',
        TRIAL_END_DATE: finalDate,
        EXPIRE_ICON: process.env.EXPIRE_ICON ?? '',
        SUPPORT_EMAIL: process.env.SUPPORT_EMAIL ?? '',
        HREF_SUPPORT: `mailto:${process.env.SUPPORT_EMAIL}`,
      });
      const subject = `Trial Plan Ending Soon – Action Required for Tenant ${tenant.name}`;
      const ccEmails = getCCEmailsForEvent(template.eventName);
      return new Notification({
        subject,
        body,
        receiver: {
          to: [{id: tenant.id, toEmail: tenant.contacts?.[0]?.email ?? ''}],
          cc: ccEmails,
        },
        type: 1,
        sentDate: new Date(),
        options: {fromEmail: process.env.NOTIFICATION_FROM_EMAIL},
      });
    });

    await this.notificationProxyService.createBulkNotification(
      `${token}`,
      notifications,
    );
  }
}
