import {RestBindings, Request, HttpErrors} from '@loopback/rest';
import {webhookHandler} from '../decorators/webhook-handler.decorator';
import {IWebhookHandler} from '../types';
import {inject} from '@loopback/context';
import {
  CryptoHelperService,
  Notification,
  NotificationEventType,
  NotificationType,
  PermissionKey,
  TemplateService,
  TenantStatus,
} from '@local/core';
import {LOGGER, ILogger} from '@sourceloop/core';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from './proxies';
import {service} from '@loopback/core';
import {AnyObject} from '@loopback/repository';
import {SubscriptionStatus} from '@sourceloop/ctrl-plane-subscription-service';

const API_KEY_REQUEST_HEADER = 'x-api-key';

/**
 * Webhook handler service implementation for processing API key events.
 *
 * @remarks
 * - Determines if the incoming webhook request
 */
@webhookHandler()
export class ApiKeyAuthService implements IWebhookHandler {
  /**
   * Creates an instance of ApiKeyAuthService.
   *
   * @param request - Incoming HTTP request
   * @param subscriptionProxyService - Proxy service to handle subscription webhooks
   * @param notificationProxyService - Proxy service to handle notifications
   * @param tenantMgmtProxyService - Proxy service for tenant management
   * @param cryptoHelperService - Service to generate temporary tokens
   * @param templateService - Service to render notification templates
   * @param logger - Logger instance for logging
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,

    @service(CryptoHelperService)
    private readonly cryptoHelperService: CryptoHelperService,

    @service(TemplateService)
    private readonly templateService: TemplateService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {}

  /**
   * Checks whether the incoming request is a Stripe webhook event.
   *
   * @returns `true` if the request contains a Stripe signature header, otherwise `false`
   */
  isApplicable(): boolean {
    this.logger.info(
      'Checking apikey webhook is applicable ' +
        this.request.headers[API_KEY_REQUEST_HEADER],
    );

    if (this.request.headers[API_KEY_REQUEST_HEADER]) return true;
    return false;
  }

  /**
   * Handles the API key webhook event.
   *
   * @remarks
   * - Delegates processing to the subscription proxy service.
   * - If the event is `INVOICE_CREATED`, it sends an invoice payment request notification.
   * - If provisioning is required, it triggers tenant provisioning.
   * - Ignores other events.
   *
   * @throws {@link HttpErrors.InternalServerError} If the required notification template is missing
   */
  async handle(): Promise<void> {
    console.info('request body in apikey auth service:---', this.request.body); // NOSONAR
    console.info(
      'request headers in apikey auth service:---',
      this.request.headers,
    ); // NOSONAR

    const buffer = this.request.body;
    const jsonString = buffer.toString('utf8');
    console.info('Webhook JSON String:---', jsonString); //NOSONAR

    // Parse string -> JSON object
    const parsed = JSON.parse(jsonString);
    console.info('Parsed Webhook JSON:---', parsed); //NOSONAR
    console.info(
      'API Key from Header:---',
      this.request.headers[API_KEY_REQUEST_HEADER],
    ); //NOSONAR
    const webhookResponse: AnyObject =
      await this.tenantMgmtProxyService.handleWebhookTenantStatus(
        parsed,
        this.request.headers[API_KEY_REQUEST_HEADER],
      );

    // Debug log for full response
    let parsedResponse = webhookResponse;

    const token = this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.SYSTEM_USER_TENANT_ID,
      tenantId: process.env.SYSTEM_USER_TENANT_ID,
      defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
      permissions: [
        PermissionKey.CreateNotification,
        PermissionKey.ViewNotificationTemplate,
        PermissionKey.ViewSubscription,
        PermissionKey.ViewPlan,
        PermissionKey.ViewCurrency,
        PermissionKey.ViewBillingCycle,
      ],
    });

    const subscriptionsResponse =
      await this.subscriptionProxyService.getSubscriptions(
        `Bearer ${token}`,
        JSON.stringify({
          where: {subscriberId: parsedResponse.id},
          include: [
            {
              relation: 'plan',
              scope: {
                include: [
                  {
                    relation: 'currency',
                  },
                  {
                    relation: 'billingCycle',
                  },
                ],
              },
            },
          ],
        }),
      );

    if (typeof webhookResponse === 'string') {
      parsedResponse = JSON.parse(webhookResponse);
      console.info('Could not parse parsedResponse as JSON:', parsedResponse); // NOSONAR
    }
    console.info('Full webhook response:', parsedResponse); // NOSONAR

    console.info('Generated token:--------', token); // NOSONAR
    // Fetch email template
    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.TenantOnboarding,
      NotificationType.EMAIL,
      token,
    );
    console.info('Fetched email template:--------', template); // NOSONAR

    if (!template) {
      throw new HttpErrors.InternalServerError(
        'Notification template  not found',
      );
    }
    let emailBody = '',
      emailSubject = '';
    if (Number(parsedResponse.status) === TenantStatus.ACTIVE) {
      // Decrypt the password for the email notification
      let decryptedPassword = '';
      try {
        if (parsedResponse.contacts[0]?.password) {
          decryptedPassword = this.cryptoHelperService.decryptPassword(
            parsedResponse.contacts[0].password,
          );
        }
      } catch (error) {
        console.error('Failed to decrypt password:', error); // NOSONAR
      }

      const capitalize = (str: string): string =>
        str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      const getCost = (cost: number): string =>
        (subscriptionsResponse[0]?.plan?.currency?.symbol ?? '') + cost;

      const statusValue =
        SubscriptionStatus[subscriptionsResponse[0]?.status] ?? '';
      emailSubject = 'Tenant Provisioning Successful';
      emailBody = this.templateService.generateEmail(template.body, {
        emailDetails:
          'This is to notify that tenant onboarding is completed. Tenat provisioning completed. Below are the tenant and subscription details.',
        tenantName: parsedResponse.name,
        tenantId: parsedResponse.id,
        subscriptionPlan: subscriptionsResponse[0]?.plan?.name ?? '',
        subscriptionStatus: statusValue ?? '',
        subscriptionAmount: getCost(subscriptionsResponse[0]?.totalCost) ?? '',
        noOfUsers: subscriptionsResponse[0]?.numberOfUsers ?? 'NA',
        tenure:
          capitalize(
            subscriptionsResponse[0]?.plan?.billingCycle?.cycleName?.toLowerCase(),
          ) ?? '',
        adminName: `${parsedResponse.contacts[0]?.firstName} ${parsedResponse.contacts[0]?.lastName}`,
        adminEmail: parsedResponse.contacts[0]?.email ?? '',
        username: parsedResponse.contacts[0]?.userName ?? '',
        password: decryptedPassword,
      });
      console.info('Generated email body for active:--------', emailBody); // NOSONAR
    } else if (Number(parsedResponse.status) === TenantStatus.PROVISIONFAILED) {
      emailSubject = 'Tenant Provisioning Failed';
      emailBody = this.templateService.generateEmail(template.body, {
        emailDetails:
          'This is to notify that tenant provisioning is failed. Below are the tenant details.',

        // paymentLink:  'tenant_provision_failed',
        tenantName: parsedResponse.name,
        tenantId: parsedResponse.id,

        adminName: `${parsedResponse.contacts[0]?.firstName} ${parsedResponse.contacts[0]?.lastName}`,
        adminEmail: parsedResponse.contacts[0]?.email ?? '',
      });
      console.info('Generated email body for failed:--------', emailBody); // NOSONAR
    } else {
      // No operation for other events
    }

    const toRecipients = [
      {
        id: parsedResponse.contacts[0]?.id ?? '',
        toEmail: parsedResponse.contacts[0]?.email ?? '',
      },
    ];

    // Add the second recipient only if status is NOT ACTIVE
    if (Number(parsedResponse.status) === TenantStatus.PROVISIONFAILED) {
      toRecipients.push({
        id: parsedResponse.contacts[0]?.id ?? '',
        toEmail: process.env.STAFF_EMAIL ?? '',
      });
    }
    const notification: Notification = new Notification({
      subject: emailSubject,
      body: emailBody,
      receiver: {
        to: toRecipients,
      },
      type: 1,
      sentDate: new Date(),
      options: {
        fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
      },
    });
    console.info('Final notification object:--------', notification); // NOSONAR
    await this.notificationProxyService.createNotification(token, notification);
    console.info('Notification sent successfully'); // NOSONAR
  }
}
