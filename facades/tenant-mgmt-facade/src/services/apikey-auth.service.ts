import {RestBindings, Request} from '@loopback/rest';
import {webhookHandler} from '../decorators/webhook-handler.decorator';
import {IWebhookHandler} from '../types';
import {EmailStrategyFactory} from '../strategies/email.strategy';
import {inject} from '@loopback/context';
import {
  CryptoHelperService,
  getCCEmailsForEvent,
  Notification,
  PermissionKey,
  TemplateService,
} from '@local/core';
import {LOGGER, ILogger} from '@sourceloop/core';
import {
  NotificationProxyService,
  SubscriptionProxyService,
  TenantMgmtProxyService,
} from './proxies';
import {service} from '@loopback/core';
import {AnyObject} from '@loopback/repository';
import {SubscriptionWithRelations} from '../models';

const API_KEY_REQUEST_HEADER = 'x-api-key';

/**
 * Webhook handler service implementation for processing API key events.
 *
 * @remarks
 * - Determines if the incoming webhook request
 */
@webhookHandler()
export class ApiKeyAuthService implements IWebhookHandler {
  /**
   * Creates an instance of ApiKeyAuthService.
   *
   * @param request - Incoming HTTP request
   * @param subscriptionProxyService - Proxy service to handle subscription webhooks
   * @param notificationProxyService - Proxy service to handle notifications
   * @param tenantMgmtProxyService - Proxy service for tenant management
   * @param cryptoHelperService - Service to generate temporary tokens
   * @param templateService - Service to render notification templates
   * @param logger - Logger instance for logging
   */
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject('services.SubscriptionProxyService')
    private readonly subscriptionProxyService: SubscriptionProxyService,

    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,
    @inject('services.TenantMgmtProxyService')
    private readonly tenantMgmtProxyService: TenantMgmtProxyService,

    @service(CryptoHelperService)
    private readonly cryptoHelperService: CryptoHelperService,

    @service(TemplateService)
    private readonly templateService: TemplateService,
    @inject(LOGGER.LOGGER_INJECT)
    private readonly logger: ILogger,
  ) {}

  /**
   * Checks whether the incoming request is a Stripe webhook event.
   *
   * @returns `true` if the request contains a Stripe signature header, otherwise `false`
   */
  isApplicable(): boolean {
    this.logger.info(
      'Checking apikey webhook is applicable ' +
        this.request.headers[API_KEY_REQUEST_HEADER],
    );

    if (this.request.headers[API_KEY_REQUEST_HEADER]) return true;
    return false;
  }

  /**
   * Handles the API key webhook event.
   *
   * @remarks
   * - Delegates processing to the subscription proxy service.
   * - If the event is `INVOICE_CREATED`, it sends an invoice payment request notification.
   * - If provisioning is required, it triggers tenant provisioning.
   * - Ignores other events.
   *
   * @throws {@link HttpErrors.InternalServerError} If the required notification template is missing
   */
  async handle(): Promise<void> {
    const buffer = this.request.body;
    const jsonString = buffer.toString('utf8');
    const parsed = JSON.parse(jsonString);
    const apiKey = this.request.headers[API_KEY_REQUEST_HEADER];

    const {url, eventType, secret} = parsed;
    console.log({url, eventType}); // NOSONAR
    delete parsed.url;
    delete parsed.secret;
    delete parsed.eventType;

    console.info('Parsed Webhook Request:---', parsed); // NOSONAR
    let webhookResponse: AnyObject =
      await this.tenantMgmtProxyService.handleWebhookTenantStatus(
        parsed,
        apiKey,
      );
    console.info('Webhook Response:---', webhookResponse); // NOSONAR
    if (typeof webhookResponse === 'string') {
      webhookResponse = JSON.parse(webhookResponse);
    }

    console.info('Parsed Webhook Response:---', webhookResponse);

    const token = this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.SYSTEM_USER_TENANT_ID,
      tenantId: process.env.SYSTEM_USER_TENANT_ID,
      defaultTenantId: process.env.SYSTEM_USER_TENANT_ID,
      permissions: [
        PermissionKey.CreateNotification,
        PermissionKey.ViewNotificationTemplate,
        PermissionKey.ViewSubscription,
        PermissionKey.UpdateSubscription,
        PermissionKey.ViewPlan,
        PermissionKey.ViewCurrency,
        PermissionKey.ViewBillingCycle,
        PermissionKey.ViewTenantBillings,
      ],
    });

    const subscriptionsResponse = await this.getSubscriptions(
      token,
      webhookResponse.id,
    );

    webhookResponse.url = url;
    webhookResponse.secret = secret;
    webhookResponse.eventType = eventType;
    const {
      emailSubject,
      emailBody,
      eventType: templateEventType,
    } = await this.getTemplateAndGenerateEmail(
      webhookResponse,
      subscriptionsResponse,
      token,
    );
    if (!emailBody || !emailSubject) return;

    const toRecipients = this.buildRecipients(webhookResponse);
    let ccEmails: {toEmail: string}[] = [];
    if (templateEventType) {
      ccEmails = getCCEmailsForEvent(templateEventType);
    }
    const notification = new Notification({
      subject: emailSubject,
      body: emailBody,
      receiver: {
        to: toRecipients,
        cc: ccEmails,
      },
      type: 1,
      sentDate: new Date(),
      options: {
        fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
      },
    });
    console.info('Notification Object:---', notification);

    await this.notificationProxyService.createNotification(token, notification);
    console.info('Notification sent successfully'); // NOSONAR
  }

  private async getSubscriptions(
    token: string,
    subscriberId: string,
  ): Promise<SubscriptionWithRelations[]> {
    const filter = {
      where: {subscriberId},
      include: [
        {
          relation: 'plan',
          scope: {
            include: [
              {relation: 'currency'},
              {relation: 'billingCycle'},
              {relation: 'configureDevice'},
            ],
          },
        },
      ],
    };

    return this.subscriptionProxyService.getSubscriptions(
      `Bearer ${token}`,
      JSON.stringify(filter),
    );
  }

  private async getTemplateAndGenerateEmail(
    parsedResponse: AnyObject,
    subscriptionsResponse: SubscriptionWithRelations[],
    token: string,
  ): Promise<{emailSubject: string; emailBody: string; eventType?: string}> {
    const services = {
      notificationProxyService: this.notificationProxyService,
      subscriptionProxyService: this.subscriptionProxyService,
      templateService: this.templateService,
      cryptoHelperService: this.cryptoHelperService,
      logger: this.logger,
    };

    const strategy = EmailStrategyFactory.getStrategy(
      parsedResponse.eventType,
      Number(parsedResponse.status),
    );

    const result = await strategy.generate(
      parsedResponse,
      subscriptionsResponse,
      token,
      services,
    );
    return {
      ...result,
      eventType: parsedResponse.eventType,
    };
  }

  private buildRecipients(
    parsedResponse: AnyObject,
  ): {id: string; toEmail: string}[] {
    const recipients = [
      {
        id: parsedResponse.contacts[0]?.id ?? '',
        toEmail: parsedResponse.contacts[0]?.email ?? '',
      },
    ];

    return recipients;
  }
}
