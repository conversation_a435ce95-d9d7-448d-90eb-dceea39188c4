import {
  applicationRateLimitKeyGen,
  AuthorizeActionProvider,
  AwsS3Provider,
  CryptoHelperService,
  FileMetadataProvider,
  TemplateService,
  permissionPolicyMiddleware,
} from '@local/core';
import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import * as dotenv from 'dotenv';
import * as dotenvExt from 'dotenv-extended';
import {AuthenticationComponent} from 'loopback4-authentication';
import {
  AuthorizationBindings,
  AuthorizationComponent,
} from 'loopback4-authorization';
import {HelmetSecurityBindings} from 'loopback4-helmet';
import {RateLimitSecurityBindings} from 'loopback4-ratelimiter';
import {
  CoreComponent,
  SecureSequence,
  AuthCacheSourceName,
  SFCoreBindings,
  BearerVerifierBindings,
  BearerVerifierComponent,
  BearerVerifierConfig,
  BearerVerifierType,
  SECURITY_SCHEME_SPEC,
  CoreConfig,
} from '@sourceloop/core';
import {RepositoryMixin} from '@loopback/repository';
import {ExpressRequestHandler, RestApplication} from '@loopback/rest';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'node:path';
import * as openapi from './openapi.json';
import {FileUtilBindings, FileUtilComponent} from '@sourceloop/file-utils';
import {FileUploadLimitsService} from './services';
import {AWSS3Bindings, MulterS3Storage} from '@sourceloop/file-utils/s3';

import {ClamAVValidator} from '@sourceloop/file-utils/clamav';
import compression from 'compression';
import {InputValidateInterceptor} from './interceptor/input-validator.interceptor';

export {ApplicationConfig};

export class TenantMgmtFacadeApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    const port = 3000;
    dotenv.config();
    dotenvExt.load({
      schema: '.env.example',
      errorOnMissing: process.env.NODE_ENV !== 'test',
      includeProcessEnv: true,
    });

    // Configure REST options before calling super()
    options.rest = options.rest ?? {};
    options.rest.basePath = process.env.BASE_PATH ?? '';
    options.rest.port = +(process.env.PORT ?? port);
    options.rest.host = process.env.HOST;
    options.rest.openApiSpec = {
      endpointMapping: {
        [`${options.rest.basePath}/openapi.json`]: {
          version: '3.0.0',
          format: 'json',
        },
      },
    };
    options.rest.cors = {origin: process.env.ALLOWED_ORIGINS};
    super(options);

    this.configureGZipAndPermissionPolicy();
    this.configureCoreComponents();
    this.configureInterceptor();
    this.configureSecurityBindings();
    this.configureAuthenticationAndAuthorization();
    this.configureFileServices(options);
    this.configureStaticAssets();
    this.configureBootOptions();
    this.configureApiSpec();
  }

  private configureGZipAndPermissionPolicy() {
    this.middleware(compression);
    // Ensure expressMiddlewares binding exists before getting it
    let expressMiddlewares: ExpressRequestHandler[] = [];
    try {
      expressMiddlewares =
        this.getSync(SFCoreBindings.EXPRESS_MIDDLEWARES) ?? [];
    } catch {
      // Not bound yet, initialize as empty array
      expressMiddlewares = [];
    }
    expressMiddlewares.push(
      compression({level: 1, memLevel: 9, chunkSize: 30000}),
      permissionPolicyMiddleware,
    );
    this.bind(SFCoreBindings.EXPRESS_MIDDLEWARES).to(expressMiddlewares);
  }

  private configureInterceptor() {
    this.interceptor(InputValidateInterceptor, {global: true});
  }

  private configureCoreComponents(): void {
    // To check if monitoring is enabled from env or not
    const enableObf = !!+(process.env.ENABLE_OBF ?? 0);
    // To check if authorization is enabled for swagger stats or not
    const authentication = !!(
      process.env.SWAGGER_USER && process.env.SWAGGER_PASSWORD
    );
    const obj: CoreConfig = {
      enableObf,
      obfPath: process.env.OBF_PATH ?? '/obf',
      openapiSpec: openapi,
      authentication: authentication,
      authenticateSwaggerUI: authentication,
      swaggerUsername: process.env.SWAGGER_USER,
      swaggerPassword: process.env.SWAGGER_PASSWORD,
    };

    this.bind(SFCoreBindings.config).to(obj);
    this.component(CoreComponent);

    // Set up the custom sequence
    this.sequence(SecureSequence);
  }

  private configureSecurityBindings(): void {
    this.bind(HelmetSecurityBindings.CONFIG).to({
      referrerPolicy: {
        policy: 'same-origin',
      },
      contentSecurityPolicy: {
        directives: {
          frameSrc: ["'self'"],
          scriptSrc: ["'self'", `'${process.env.CSP_SCRIPT_SRC_HASH ?? ''}'`],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    });

    this.bind(RateLimitSecurityBindings.CONFIG).to({
      name: AuthCacheSourceName,
      max: Number.parseInt(process.env.RATE_LIMIT_REQUEST_CAP ?? '100'),
      keyGenerator: applicationRateLimitKeyGen,
    });
  }

  private configureAuthenticationAndAuthorization(): void {
    // Add authentication component
    this.component(AuthenticationComponent);

    // Add bearer verifier component
    this.bind(BearerVerifierBindings.Config).to({
      type: BearerVerifierType.facade,
      useSymmetricEncryption: true,
    } as BearerVerifierConfig);
    this.component(BearerVerifierComponent);
    this.service(CryptoHelperService);
    this.service(TemplateService);

    // Add authorization component
    this.bind(AuthorizationBindings.CONFIG).to({
      allowAlwaysPaths: ['/explorer', '/openapi.json'],
    });
    this.component(AuthorizationComponent);
    this.bind(AuthorizationBindings.AUTHORIZE_ACTION.key).toProvider(
      AuthorizeActionProvider,
    );
  }

  private configureFileServices(options: ApplicationConfig): void {
    if (!options.noAv) {
      this.service(ClamAVValidator);
    }

    // Configure S3 for LocalStack
    const s3Config = {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID ?? 'test',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ?? 'test',
      region: process.env.AWS_REGION ?? 'us-east-1',
      ...(process.env.AWS_ENDPOINT_URL && {
        endpoint: process.env.AWS_ENDPOINT_URL,
        forcePathStyle: true, // Required for LocalStack
      }),
    };

    this.bind('services.CryptoHelperService').toClass(CryptoHelperService);
    this.bind(AWSS3Bindings.Config).to(s3Config);
    this.bind(AWSS3Bindings.AwsS3Provider).toProvider(AwsS3Provider);
    this.component(FileUtilComponent);
    this.bind(FileUtilBindings.LimitProvider).toClass(FileUploadLimitsService);

    this.service(MulterS3Storage);
    this.bind(FileUtilBindings.FILE_REQUEST_METADATA.key).toProvider(
      FileMetadataProvider,
    );
  }

  private configureStaticAssets(): void {
    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });

    this.component(RestExplorerComponent);
  }

  private configureBootOptions(): void {
    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
      services: {
        dirs: ['services'],
        extensions: ['.service.js', '.provider.js'],
        nested: true,
      },
    };
  }

  private configureApiSpec(): void {
    this.api({
      openapi: '3.0.0',
      info: {
        title: 'tenant-mgmt-facade',
        version: '1.0.0',
      },
      paths: {},
      components: {
        securitySchemes: SECURITY_SCHEME_SPEC,
      },
      servers: [{url: '/'}],
    });
  }
}
