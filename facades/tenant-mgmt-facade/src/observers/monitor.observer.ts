import {inject, lifeCycleObserver, LifeCycleObserver} from '@loopback/core';
import {ILogger, LOGGER} from '@sourceloop/core';
import {SubscriptionHelperService} from '../services/subscription-helper.service';

export interface TaskInterface {
  key: string;
  callback: () => Promise<void>;
  intervalTime: number;
}

/**
 * This class will be bound to the application as a `LifeCycleObserver` during
 * `boot`
 */
@lifeCycleObserver('monitor')
export class MonitorObserver implements LifeCycleObserver {
  private intervals: Record<string, ReturnType<typeof setInterval>> = {};

  constructor(
    @inject('services.SubscriptionHelperService')
    private readonly subscriptionHelperService: SubscriptionHelperService,
    @inject(LOGGER.LOGGER_INJECT)
    private logger: ILogger,
  ) {}

  // Helper function to parse interval time from an environment variable
  getInterval = (envVar: string, defaultValue = '600000'): number =>
    parseInt(process.env[envVar] ?? defaultValue, 10);

  /**
   * This method will be invoked when the application starts.
   */
  async start(): Promise<void> {
    // Add your logic for start
    this.logger.info('Monitor Observer Started');
    // Parse the interval times
    const intervalTrialPlanTime = this.getInterval('TRIAL_PLAN_INTERVAL');

    const tasks: TaskInterface[] = [
      {
        key: 'trialPlanMonitor',
        callback: async () => this.subscriptionHelperService.updateTrialPlan(),
        intervalTime: intervalTrialPlanTime,
      },
    ];
    tasks.forEach(task => {
      this.intervals[task.key] = this.startInterval(
        task.callback,
        task.intervalTime,
      );
    });
  }

  private startInterval(
    callback: () => Promise<void>,
    intervalTime: number,
  ): ReturnType<typeof setInterval> {
    return setInterval(() => {
      callback().catch(error => {
        this.logger.error(JSON.stringify(error.message));
      });
    }, intervalTime);
  }

  /**
   * This method will be invoked when the application stops.
   */
  async stop(): Promise<void> {
    // Add your logic for stop
    if (this.intervals) {
      Object.keys(this.intervals).forEach(key => {
        clearInterval(this.intervals[key]);
        this.logger.info(`Stopped ${key} interval`);
      });
      this.intervals = {}; // Clear stored interval references
    }
  }
}
