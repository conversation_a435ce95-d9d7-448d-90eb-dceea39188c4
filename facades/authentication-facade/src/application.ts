import {
  applicationRateLimitKeyGen,
  AuthorizeActionProvider,
  permissionPolicyMiddleware,
} from '@local/core';

import {BootMixin} from '@loopback/boot';
import {ApplicationConfig} from '@loopback/core';
import {
  RestExplorerBindings,
  RestExplorerComponent,
} from '@loopback/rest-explorer';
import * as dotenv from 'dotenv';
import * as dotenvExt from 'dotenv-extended';
import {AuthenticationComponent} from 'loopback4-authentication';
import {
  AuthorizationBindings,
  AuthorizationComponent,
} from 'loopback4-authorization';
import {HelmetSecurityBindings} from 'loopback4-helmet';
import {
  CoreComponent,
  SecureSequence,
  AuthCacheSourceName,
  SFCoreBindings,
  BearerVerifierBindings,
  BearerVerifierComponent,
  BearerVerifierConfig,
  BearerVerifierType,
  SECURITY_SCHEME_SPEC,
  CoreConfig,
} from '@sourceloop/core';
import {RepositoryMixin} from '@loopback/repository';
import {RestApplication, ExpressRequestHandler} from '@loopback/rest';
import {ServiceMixin} from '@loopback/service-proxy';
import path from 'path';
import compression from 'compression';
import * as openapi from './openapi.json';
import {
  RateLimiterComponent,
  RateLimitSecurityBindings,
} from 'loopback4-ratelimiter';

import {ForgetPasswordTokenVerifierProvider} from './providers';
import {FORGET_PASSWORD_TOKEN_VERIFIER} from './key';
import {InputValidateInterceptor} from './interceptor/input-validator.interceptor';

export {ApplicationConfig};

export class AuthenticationFacadeApplication extends BootMixin(
  ServiceMixin(RepositoryMixin(RestApplication)),
) {
  constructor(options: ApplicationConfig = {}) {
    const port = 3000;
    dotenv.config();
    dotenvExt.load({
      schema: '.env.example',
      errorOnMissing: process.env.NODE_ENV !== 'test',
      includeProcessEnv: true,
    });
    options.rest = options.rest ?? {};
    options.rest.basePath = process.env.BASE_PATH ?? '';
    options.rest.port = +(process.env.PORT ?? port);
    options.rest.host = process.env.HOST;
    options.rest.openApiSpec = {
      endpointMapping: {
        [`${options.rest.basePath}/openapi.json`]: {
          version: '3.0.0',
          format: 'json',
        },
      },
    };
    options.rest.cors = {origin: process.env.ALLOWED_ORIGINS};
    // Trust proxy for proper IP extraction behind proxies/load balancers
    options.rest.trustProxy = true;
    super(options);

    this.configureGZipAndPermissionPolicy();
    this.configureInterceptor();

    // To check if monitoring is enabled from env or not
    const enableObf = !!+(process.env.ENABLE_OBF ?? 0);
    // To check if authorization is enabled for swagger stats or not
    const authentication = !!(
      process.env.SWAGGER_USER && process.env.SWAGGER_PASSWORD
    );
    const obj: CoreConfig = {
      enableObf,
      obfPath: process.env.OBF_PATH ?? '/obf',
      openapiSpec: openapi,
      authentication: authentication,
      authenticateSwaggerUI: authentication,
      swaggerUsername: process.env.SWAGGER_USER,
      swaggerPassword: process.env.SWAGGER_PASSWORD,
    };
    this.bind(SFCoreBindings.config).to(obj);
    this.component(CoreComponent);

    // Set up the custom sequence
    this.sequence(SecureSequence);

    this.bind(HelmetSecurityBindings.CONFIG).to({
      referrerPolicy: {
        policy: 'same-origin',
      },
      contentSecurityPolicy: {
        directives: {
          frameSrc: ["'self'"],
          scriptSrc: ["'self'", `'${process.env.CSP_SCRIPT_SRC_HASH ?? ''}'`],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    });

    this.bind(RateLimitSecurityBindings.CONFIG).to({
      name: AuthCacheSourceName,
      max: parseInt(process.env.RATE_LIMIT_REQUEST_CAP ?? '100'),
      keyGenerator: applicationRateLimitKeyGen,
    });

    this.component(RateLimiterComponent);

    // Add authentication component
    this.component(AuthenticationComponent);

    // Add bearer verifier component
    this.bind(BearerVerifierBindings.Config).to({
      type: BearerVerifierType.facade,
      useSymmetricEncryption: true,
    } as BearerVerifierConfig);
    this.component(BearerVerifierComponent);
    // Add authorization component

    this.bind(AuthorizationBindings.CONFIG).to({
      allowAlwaysPaths: ['/explorer', '/openapi.json'],
    });
    this.component(AuthorizationComponent);
    this.bind(AuthorizationBindings.AUTHORIZE_ACTION.key).toProvider(
      AuthorizeActionProvider,
    );

    this.bind(FORGET_PASSWORD_TOKEN_VERIFIER).toProvider(
      ForgetPasswordTokenVerifierProvider,
    );
    // Set up default home page
    this.static('/', path.join(__dirname, '../public'));

    // Customize @loopback/rest-explorer configuration here
    this.configure(RestExplorerBindings.COMPONENT).to({
      path: '/explorer',
    });

    this.component(RestExplorerComponent);

    this.projectRoot = __dirname;
    // Customize @loopback/boot Booter Conventions here
    this.bootOptions = {
      controllers: {
        // Customize ControllerBooter Conventions here
        dirs: ['controllers'],
        extensions: ['.controller.js'],
        nested: true,
      },
      services: {
        dirs: ['services'],
        extensions: ['.service.js', '.provider.js'],
        nested: true,
      },
    };

    this.api({
      openapi: '3.0.0',
      info: {
        title: 'authentication-facade',
        version: '1.0.0',
      },
      paths: {},
      components: {
        securitySchemes: SECURITY_SCHEME_SPEC,
      },
      servers: [{url: '/'}],
    });
  }

  private configureInterceptor() {
    this.interceptor(InputValidateInterceptor, {global: true});
  }

  private configureGZipAndPermissionPolicy() {
    this.middleware(compression);
    // Ensure expressMiddlewares binding exists before getting it
    let expressMiddlewares: ExpressRequestHandler[] = [];
    try {
      expressMiddlewares =
        this.getSync(SFCoreBindings.EXPRESS_MIDDLEWARES) ?? [];
    } catch {
      // Not bound yet, initialize as empty array
      expressMiddlewares = [];
    }
    expressMiddlewares.push(
      compression({level: 1, memLevel: 9, chunkSize: 30000}),
    );
    expressMiddlewares.push(permissionPolicyMiddleware);
    this.bind(SFCoreBindings.EXPRESS_MIDDLEWARES).to(expressMiddlewares);
  }
}
