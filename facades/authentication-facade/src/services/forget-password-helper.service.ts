import {
  getCCEmailsForEvent,
  NotificationEventType,
  NotificationTemplates,
  NotificationType,
  PermissionKey,
  UpdatePasswordDto as ServiceUpdatePasswordDto,
} from '@local/core';
import {BindingScope, inject, injectable} from '@loopback/context';
import {service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors, Request, RestBindings} from '@loopback/rest';
import {AuthenticationBindings} from '@sourceloop/authentication-service';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {Notification} from '../models';
import {ForgetPasswordRequestDto, UpdatePasswordDto} from '../models/dto';
import {UserTokenRepository} from '../repositories/user-token.repository';
import {AuthenticatorService} from './authenticator.service';
import {CryptoHelperService} from './crypto-helper.service';
import {NotificationProxyService, UserTenantProxyService} from './proxies';
import {AuthenticationProxyService} from './proxies/authentication-proxy.provider';
import {TemplateService} from './template.service';
import {convertMsToTime} from '../utils';
import {UserView} from '../models/user-view.model';

/**
 * Service that handles the forget-password and password reset functionality.
 */
@injectable({scope: BindingScope.TRANSIENT})
export class ForgetPasswordHelperService {
  /**
   * Constructor for ForgetPasswordHelperService.
   *
   * @param authProxyService - Service to handle user authentication operations.
   * @param userTenantProxyService - Service to retrieve user-tenant mappings.
   * @param cryptoHelperService - Utility service for token generation.
   * @param notificationProxyService - Service to send out email/notification.
   * @param templateService - Service to generate email templates.
   * @param authenticatorService - Service to manage temp password reset tokens.
   * @param request - The current HTTP request object.
   * @param currentUser - The currently authenticated user.
   * @param userTokenRepository - Repository for managing user tokens.
   */
  constructor(
    @inject(`services.AuthenticationProxyService`)
    private readonly authProxyService: AuthenticationProxyService,

    @inject(`services.UserTenantProxyService`)
    private readonly userTenantProxyService: UserTenantProxyService,

    @service(CryptoHelperService)
    private readonly cryptoHelperService: CryptoHelperService,

    @inject(`services.NotificationProxyService`)
    private readonly notificationProxyService: NotificationProxyService,

    @service(TemplateService)
    private readonly templateService: TemplateService,

    @service(AuthenticatorService)
    private readonly authenticatorService: AuthenticatorService,

    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,

    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,

    @repository(UserTokenRepository)
    public userTokenRepository: UserTokenRepository,
  ) {}

  /**
   * Initiates the forget password process by validating the user,
   * generating a temporary reset token, and sending a reset email.
   *
   * @param dto - Data Transfer Object containing user's email.
   * @throws HttpErrors.NotFound if user is not found.
   * @throws HttpErrors.InternalServerError if required env variables are missing.
   */
  async forgetPassword(dto: ForgetPasswordRequestDto): Promise<void> {
    if (!process.env.DEFAULT_TENANT_ID) {
      throw new HttpErrors.InternalServerError(
        'DEFAULT_TENANT_ID is not defined in environment variables',
      );
    }

    const token = this.cryptoHelperService.generateTempToken({
      userTenantId: process.env.CLIENT_ID,
      tenantId: process.env.DEFAULT_TENANT_ID,
      defaultTenantId: process.env.DEFAULT_TENANT_ID,
      permissions: [
        PermissionKey.UpdatePassword,
        PermissionKey.CreateNotification,
        PermissionKey.ViewTenantUser,
        PermissionKey.ViewNotificationTemplate,
      ],
    });

    const users = await this.userTenantProxyService.find(
      token,
      process.env.DEFAULT_TENANT_ID,
      {
        where: {
          email: dto.email,
        },
      },
    );

    if (!users || users.length === 0) {
      return;
    }

    const user = users[0];

    // Generate and store temporary token
    const key =
      await this.authenticatorService.setForgetPasswordTempToken(user);

    // Construct reset link
    const baseResetLink = process.env.RESET_LINK ?? '';
    const resetLinkWithCode = `${baseResetLink}?code=${encodeURIComponent(key)}`;
    const template = await this.notificationProxyService.getTemplateByName(
      NotificationEventType.ForgetPassword,
      NotificationType.EMAIL,
      token,
    );
    if (!template) {
      throw new HttpErrors.InternalServerError(
        'Notification template for forget password not found',
      );
    }

    const expireTime = +(process.env.FORGET_PASSWORD_LINK_EXPIRY ?? '300'); // fallback to 300 seconds
    const formattedExpiry = convertMsToTime(expireTime);

    // Generate email content using template
    const emailBody = this.generateResetPasswordEmailBody(
      template,
      user,
      resetLinkWithCode,
      formattedExpiry,
    );

    const ccEmails = getCCEmailsForEvent(template.eventName);

    // Prepare and send notification
    const notification: Notification = new Notification({
      subject: template.subject.replace(
        '%S',
        process.env.ORGINATION_NAME ?? 'Distek',
      ),
      body: emailBody,
      receiver: {
        to: [
          {
            id: user.id ?? '',
            toEmail: dto.email,
            cc: ccEmails,
          },
        ],
      },
      type: 1,
      sentDate: new Date(),
      options: {
        fromEmail: process.env.NOTIFICATION_FROM_EMAIL,
      },
    });

    await this.notificationProxyService.createNotification(token, notification);
  }

  /**
   * Updates the user's password by calling the authentication proxy,
   * and deletes the temporary token after successful reset.
   *
   * @param dto - DTO containing the new password.
   * @throws HttpErrors.InternalServerError if client credentials are missing.
   * @throws HttpErrors.BadRequest if authorization header is missing.
   */
  async updatePassword(dto: UpdatePasswordDto): Promise<void> {
    const clientId = process.env.CLIENT_ID;
    const clientSecret = process.env.CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new HttpErrors.InternalServerError(
        'CLIENT_ID or CLIENT_SECRET is not defined in environment variables',
      );
    }

    // Generate token with required permissions
    const token = this.cryptoHelperService.generateTempToken({
      id: this.currentUser.id,
      userTenantId: this.currentUser.userTenantId,
      tenantId: this.currentUser.tenantId,
      permissions: [PermissionKey.UpdatePassword],
    });

    // Update the password through the proxy
    await this.authProxyService.updatePassword(
      token,
      new ServiceUpdatePasswordDto({
        newPassword: dto.newPassword,
        clientId: clientId,
        clientSecret: clientSecret,
      }),
    );

    // Remove used token from repository
    const authorizationHeader = this.request.headers.authorization?.replace(
      /^Bearer\s+/i,
      '',
    );

    if (!authorizationHeader) {
      throw new HttpErrors.BadRequest('Authorization header is missing');
    }

    await this.userTokenRepository.delete(authorizationHeader);
  }
  /**
   * Generates the email body for the reset password email.
   */
  private generateResetPasswordEmailBody(
    template: NotificationTemplates,
    user: UserView,
    resetLinkWithCode: string,
    formattedExpiry: string,
  ): string {
    return this.templateService.generateEmail(template.body, {
      DISTEK_LOGO: process.env.DISTEK_LOGO ?? '',
      USER_NAME: user.firstName ?? '',
      KEY_ICON: process.env.KEY_ICON ?? '',
      RESET_LINK: resetLinkWithCode,
      EXPIRY_TIME: formattedExpiry,
      SUPPORT_EMAIL: process.env.SUPPORT_EMAIL ?? '',
      HREF_SUPPORT: `mailto:${process.env.SUPPORT_EMAIL}`,
      BEST_REGARDS_BY: process.env.BEST_REGARDS_BY ?? 'Distek Team',
    });
  }
}
